variables:
  PATCH_STAGE: "default"
  PUBLISH: "false"
  SYNC: "false"
  DR: "false"
  RUN_ONLY: "DISABLE_DATA_VAULT"
  PATCH_VERSION: ""
stages:
  - disable_data_vault
 
.exec_template:
  allow_failure: true
  tags:
    - barnyard
  except:
    - merge_requests

.disableDataVault_template:
  extends: .exec_template
  stage: disable_data_vault
  
###################################################
# ci/cd jobs
###################################################
disable_data_vault:
  extends: .disableDataVault_template
  stage: disable_data_vault
  variables:
    PATCH_STAGE: "disableDataVault"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][4-9]).([0-9]|[1-9][0-9]).([0-9]|[1-9][0-9])/ && $RUN_ONLY =~ /DISABLE_DATA_VAULT/



