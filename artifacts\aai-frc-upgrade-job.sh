#!/bin/bash
set -e 

source ./common_functions.sh
chmod 775 aai_frc_upgrade.py
initialize(){	
mkdir -p ./env/$MSP_PROJ_NAME
artstore -s -e k8s -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY} file pull -rf ${DEP_ARTIFACTS_PATH}/env/$MSP_PROJ_NAME/pipeline-$OST_ENVIRONMENT.env ./env/$MSP_PROJ_NAME/ 
mv ./env/$MSP_PROJ_NAME/pipeline-$OST_ENVIRONMENT.env pipeline.env
source ./pipeline.env	
artstore -s -e k8s -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY} file pull -rf ${DEP_ARTIFACTS_PATH}/aai_image_versions.props ./ 

grep "^aai_frc_upgrade_version=" aai_image_versions.props > aai_frc_env.props
if [ $? -ne 0 ]; then
    logMessagePatch "ERROR" "Entry 'aai_frc_upgrade_version' has not been found in aai_image_versions.props."
    exit -1
fi

echo "base_secret_url=${BASE_SECRET_URLS}" >> aai_frc_env.props
echo "erf_dns=${AAI_ERF_DNS}" >> aai_frc_env.props
echo "tenant_id=$TENANT_ID" >> aai_frc_env.props
echo "ci_pipeline_id=$CI_PIPELINE_ID" >> aai_frc_env.props
echo "api_poll_count=$api_poll_count" >> aai_frc_env.props
echo "api_poll_interval=$api_poll_interval" >> aai_frc_env.props
echo "msp_proj=$MSP_PROJ_NAME" >> aai_frc_env.props
echo "dfcs_patch_version=$PATCH_VERSION" >> aai_frc_env.props
cat aai_frc_env.props
}

#Start Execution
initialize
logMessagePatch "INFO" "aai-frc-upgrade: initialization done"
python3 -u aai_frc_upgrade.py
