#!/bin/python3

import os
import subprocess
import base64
import json
from pathlib import Path
import zipfile
import sys  
from datetime import datetime
import time
import requests
from requests.exceptions import HTTPError, Timeout, ConnectionError
import py_utils as p
import warnings
from urllib3.exceptions import InsecureRequestWarning
warnings.simplefilter("ignore", InsecureRequestWarning)


def poll_aai_upgrade_status(
    url_path: str,
    custom_headers: dict = None,
    api_poll_count: int = 3,
    api_poll_interval: int = 5  # Sleep time between attempts in seconds
) -> dict:
    p.logMessage("LOG", "Poll Upgrade status..")
    headers = {"Content-Type": "application/json"}
    
    if custom_headers:
        headers.update(custom_headers)

    for attempt in range(api_poll_count):
        try:
            response = requests.get(url_path, headers=headers, verify=False, timeout=60)
            response.raise_for_status()

            # Check for HTTP 200 OK and process the status
            if response.status_code == 200:
                response_data = response.json()
                status = response_data.get("status")
                message = response_data.get("message")

                if status == "completed":
                    return response_data  # Exit with successful response

                elif status == "error":
                    p.logMessage("ERROR", f"AAI upgrade failed - {message}")
                    raise Exception(f"AAI upgrade failed - {message}")

                # If status is "pending" or "running", continue polling
                elif status in ["pending", "running"]:
                    p.logMessage("LOG", f"Current aai upgrade status - '{status}', waiting for {api_poll_interval} seconds...")
                
                else: 
                    raise Exception(f"Unknown status received. AAI upgrade failed - {response_data}")
            else:
                raise Exception(f"Unknown HTTP status code received - {response.status_code}. AAI upgrade failed - {response.json()}")

        except Timeout:
            p.logMessage("WARNING", f"Request timed out (attempt {attempt + 1})")
        
        except ConnectionError as ce:
            raise Exception("ConnectionError: Unable to connect to the AAI service.") from ce
        
        except HTTPError as http_err:
            error_message = f"HTTPError occurred: {http_err}"
            if http_err.response is not None:
                try:
                    # Capture additional details from the response
                    error_details = http_err.response.json()
                    error_message += f" | Response: {error_details}"                    
                except ValueError:
                    # If response body is not JSON, include raw text
                    error_message += f" | Response Text: {http_err.response.text}"
                # Check for "doesn't exist" in response
                if http_err.response.status_code >= 500 and "doesn't exist" in error_message.lower():
                    raise Exception(f"Resource doesn't exist. Please contact FRC Foundation Team. Full response: {error_message}")
                p.logMessage("ERROR", error_message)
            
            if response.status_code >= 500:
                p.logMessage("WARNING", f"Server error (attempt {attempt + 1}): {http_err}")
            else:
                raise

        # Wait before retrying if we're not on the last attempt
        if attempt < api_poll_count - 1:
            time.sleep(api_poll_interval)

    # If all attempts are exhausted
    raise Exception("Exceeded maximum retry attempts.")

def initiate_aai_upgrade(
    url_path: str,
    payload: dict = None,
    custom_headers: dict = None,
    api_poll_count: int = 3,
    api_poll_interval: int = 5  # Sleep time between attempts in seconds
) -> dict:
    p.logMessage("LOG", "Initiate aai upgrade..")
    headers = {"Content-Type": "application/json"}
    
    if custom_headers:
        headers.update(custom_headers)

    for attempt in range(api_poll_count):
        try:
            response = requests.post(url_path, headers=headers, json=payload, verify=False, timeout=60)
            response.raise_for_status()

            # Check for HTTP 200 OK and process the status
            if response.status_code == 200:
                response_data = response.json()
                status = response_data.get("status")

                # If status is "pending" exit this function and poll status.
                if status in ["pending", "running"]:
                    p.logMessage("LOG", f"AAI upgrade request accepted.")  
                    return response_data          
                else: 
                    raise Exception(f"Unknown status received - {status}.AAI upgrade request failed. Response Status - {response_data}")
            else:
                raise Exception(f"Unknown HTTP status code received - {response.status_code}. AAI upgrade failed - {response.json()}")
       
        except Timeout:
            p.logMessage("WARNING", f"Request timed out (attempt {attempt + 1})")

        except ConnectionError as ce:
            raise Exception("ConnectionError: Unable to connect to the AAI service.") from ce

        except HTTPError as http_err:
            status_code = http_err.response.status_code if http_err.response else "Unknown"
            error_message = f"HTTPError occurred: {http_err}"
            if http_err.response is not None:
                try:
                    # Capture additional details from the response
                    error_details = http_err.response.json()
                    error_message += f" | Response: {error_details}"
                except ValueError:
                    # If response body is not JSON, include raw text
                    error_message += f" | Response Text: {http_err.response.text}"
            p.logMessage("ERROR", error_message)
            if status_code >= 500:
                p.logMessage("WARNING", f"Server error ({status_code}): Retrying after server error (Attempt {attempt + 1}/{api_poll_count})...")
            else:
                raise        

        # Wait before retrying if we're not on the last attempt
        if attempt < api_poll_count - 1:
            time.sleep(api_poll_interval)

    # If all attempts are exhausted
    raise Exception("Exceeded maximum retry for initiating the upgrade.")

#Start Execution
p.logMessage("LOG", "Start Execution...")
props = p.load_properties("aai_frc_env.props")

# Initialize all env variables
ci_pipeline_id = props.get("ci_pipeline_id", "")
api_poll_count = int(props.get("api_poll_count", 3))
api_poll_interval = int(props.get("api_poll_interval", 5))
afcs_patch_version = props.get("dfcs_patch_version", "")
base_secret_url = props.get("base_secret_url", "").rstrip('/')
erf_dns = props.get("erf_dns", "").rstrip('/')
tenant_id = props.get("tenant_id", "")
msp_proj = props.get("msp_proj", "")
aai_frc_upgrade_version = props.get("aai_frc_upgrade_version", "")
#fetch exec params sat and env_ocid and token to access frc api
sat = p.get_sat()
env_ocid = p.get_env_ocid(tenant_id, msp_proj)


mandatory_exec_params = {
    "base_secret_url": base_secret_url,
    "erf_dns": erf_dns,
    "tenant_id": tenant_id,
    "msp_proj": msp_proj,
    "aai_frc_upgrade_version": aai_frc_upgrade_version,
    "sat": sat ,
    "env_ocid": env_ocid
}
#Check if any empty execution params
p.is_empty_error(mandatory_exec_params)
#fetch erf token to acces api
frc_url = f"{erf_dns}/fsgbu/jobless-order/v1/servicemanager/upgrade/frc"
p.logMessage("LOG", f"FRC Foundation Patch Version - {aai_frc_upgrade_version}")
frc_token = p.get_erf_token(base_secret_url, sat)
frc_status_url = f"{erf_dns}/fsgbu/jobless-order/v1/servicemanager/upgrade/frc/status/namespace/{msp_proj}--{tenant_id}/patchname/{aai_frc_upgrade_version}/ocid/{env_ocid}"

#Make frc uptake request
frc_payload = {
  "build_version": f"{aai_frc_upgrade_version}",
  "namespace": f"fsgbu-erfplatform--{tenant_id}",
  "app_proj_name": "fsgbu-fsafnd",
  "env_ocid": f"{env_ocid}",
  "setup_script": "applyPatch.sh",
  "onboarding_project_name": "frc-upgrade-pipeline",
  "sync_app_s3": "true",
  "patch_name": f"{aai_frc_upgrade_version}",
  "redeploy": "true"
}
frc_header = {"Authorization": f"Bearer {frc_token}"}

try:    
    frc_aai_upgrade_res = initiate_aai_upgrade(frc_url, payload=frc_payload, custom_headers=frc_header)
    p.logMessage("INFO", f"{frc_aai_upgrade_res.get('message',f'No message received in the response - {frc_aai_upgrade_res}')}")
    frc_upgrade_res = poll_aai_upgrade_status(frc_status_url, api_poll_count=30, api_poll_interval=120) 
    p.logMessage("INFO", f"{frc_upgrade_res.get('message', f'No message received in the response - {frc_upgrade_res}')}")
except Exception as e:
    p.logMessage("ERROR", f"Error occurred: {e}")
    sys.exit(1)

