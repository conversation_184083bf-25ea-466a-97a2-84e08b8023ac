#!/bin/bash

chmod +x ./artstore
set -e
export CURRENT_CLUSTER=$(echo "${SUB_NAMESPACE}" | sed 's/--.*//g')	
patch_id_lowercase="$PATCH_ID-$PATCH_VERSION-$TENANT_STRING"
patch_id_lowercase=$(echo $patch_id_lowercase | tr '[:upper:]' '[:lower:]')
patch_id_lowercase="${patch_id_lowercase//./-}"
logMessagePatch "INFO" "CURRENT_NAMESPACE : $CURRENT_CLUSTER"

deploy::scripts() {
	while true;
	do 
		random_number=$(date "+%Y%m%d%H%M%S")
		kubectl -n "${SUB_NAMESPACE}" get jobs > jobs.txt
		if grep -q $random_number jobs.txt; then
			logMessagePatch "INFO" "random_number found : " $random_number
		else
			break
		fi
	done

export random_number=${random_number}	
sed -i \
		-e "s|{{PREFIX}}|${PREFIX}|g" \
		-e "s|{{REGISTRY}}|${REGISTRY}|g" \
		-e "s|__artstore_path__|${ARTSTORE_PATH}|g" \
		-e "s|__deployments_artifacts_path__|${deployments_artifacts_path}|g" \
		-e "s|__PATCH_STAGE__|${PATCH_STAGE}|g" \
		-e "s|__PATCH_ID__|${PATCH_ID}|g" \
		-e "s|__REGION__|${oci_region}|g" \
		-e "s|__REGION_CODE__|${REGION_CODE}|g" \
		-e "s|__TENANCY__|${TENANCY}|g" \
		-e "s|__WALLET_PATH__|${BASE_SECRET_URLS}${TENANT_ID}|g" \
		-e "s|__BASE_SECRET_URLS__|${BASE_SECRET_URLS}|g" \
		-e "s|__ADW__|true|g" \
		-e "s|__CURRENT_CLUSTER__|${CURRENT_CLUSTER}|g" \
		-e "s|__MSP_PROJ_NAME__|${MSP_PROJ_NAME}|g" \
		./patch-dfcs-scripts-job.yaml

chmod +x execution_wrapper.sh 
logprefix=`logMessagePatch "INFO" ""`
kubectl -n  "${SUB_NAMESPACE}" delete job dfcs-scripts-exec --ignore-not-found
kubectl -n "${SUB_NAMESPACE}" delete configmap execution-wrapper --ignore-not-found
kubectl -n "${SUB_NAMESPACE}" create configmap execution-wrapper --from-file=execution_wrapper.sh
kubectl -n "${SUB_NAMESPACE}" apply -f patch-dfcs-scripts-job.yaml | sed "s/^/${logprefix}/"
k8s_create_job "${SUB_NAMESPACE}" "dfcs-scripts-exec" "patch-dfcs-scripts-job.yaml"

}

patch::setPatchSecret() {
	
	patch_det_exist=$(kubectl -n ${SUB_NAMESPACE} get secret patch-details -o jsonpath='{.data}' --ignore-not-found=true 2>/dev/null)
	PATCH_PAYLOAD_ENC=$(cat payload.json | base64 -w 0)
	PATCH_VERSION_ENC=$(echo -n $PATCH_VERSION | base64 -w 0)
	EXECUTE_SCRIPTS_ENC=$(echo -n $EXECUTE_SCRIPTS | base64 -w 0)
	if [[ -z $patch_det_exist ]];then
		kubectl -n ${SUB_NAMESPACE} create secret generic patch-details \
				--from-literal patchVersion="${PATCH_VERSION}" \
				--from-literal scriptExecuted="${EXECUTE_SCRIPTS}" \
				--from-file payloadJson="${PATCH_PAYLOAD}" \
				--dry-run=client -o yaml | kubectl apply -f -
	else
		kubectl -n ${SUB_NAMESPACE} get secret patch-details -o json \
		| jq --arg PATCH_VERSION_ENC $PATCH_VERSION_ENC '.data["patchVersion"] |= $PATCH_VERSION_ENC' \
		| jq --arg EXECUTE_SCRIPTS_ENC $EXECUTE_SCRIPTS_ENC '.data["scriptExecuted"] |= $EXECUTE_SCRIPTS_ENC' \
		| jq --arg PATCH_PAYLOAD_ENC "$PATCH_PAYLOAD_ENC" '.data["payloadJson"] |= $PATCH_PAYLOAD_ENC' | kubectl apply -f -
	fi
	result=$?
	if [[ $result == 0 ]];then
			logMessagePatch "INFO" "patch-details secret updated syccessfully"
	fi
	
}

patch::replaceSCParams() {
if [[ $MSP_PROJ_NAME == "fsgbu-fsafnd" ]];then
	SCHEMACREATOR_SUFFIX=fsgbu-fsafnd/dfcs-fsafnd-schemacreator:${PATCH_VERSION}
	JOB_YAML="run-fsafnd-sc-scripts-job.yaml"
	JOB_NAME="run-fsafnd-sc-${patch_id_lowercase}"
	#result=`curl -s --insecure -L "$external_load_balancer/prov-utils/v1/par" | python -c 'import json,sys;obj=json.load(sys.stdin);print obj["response"]'`
	result=$(curl -s --insecure -L "$external_load_balancer/prov-utils/v1/par" | python3 -c 'import json,sys; obj=json.load(sys.stdin); print(obj["response"])')
	response=`echo "$result" | cut -d '~' -f1`
	
	if [[ ${response} == "SUCCESS"* ]];then
		
		logMessagePatch "INFO" "Shared PAR URL fetched successfully." 
		PAR_URL=`echo "$result" | cut -d '~' -f2`
		PAR_URL="${PAR_URL}"
		logMessagePatch "INFO" "PAR_URL: $PAR_URL" 
		
		mkdir -p cache
		kubectl -n ${SUB_NAMESPACE} get cm pipeline-config -ojson | jq -r '.data."pipeline.env"' > ./cache/pipeline.env
		sed -i '/PAR_URL/d' ./cache/pipeline.env
		echo -e -n "\nPAR_URL=\"${PAR_URL}\"" >> ./cache/pipeline.env
		sed -i '/^$/d' ./cache/pipeline.env
		kubectl -n ${SUB_NAMESPACE} create cm pipeline-config --from-file=./cache/pipeline.env -o yaml --dry-run=client | kubectl apply -f -
	else
		logMessagePatch "FATAL" "Failed to fetch shared PAR URL for FSAFND."
		echo "------------------"
		echo "$result"
		echo "------------------"
		exit -1
	fi

else
	
	JOB_YAML="run-dfcs-sc-scripts-job.yaml"
	SCHEMACREATOR_SUFFIX=fsgbu-dfcs/dfcs-schemacreator:${PATCH_VERSION}
	JOB_NAME="run-dfcs-sc-${patch_id_lowercase}"
	logMessagePatch "INFO" "Fetching Shared PAR URL available."
	#result=`curl -s --insecure -L "https://${primary_lb_par_host}/prov-utils/v1/par" | python -c 'import json,sys;obj=json.load(sys.stdin);print obj["response"]'`
	result=$(curl -s --insecure -L "https://${primary_lb_par_host}/prov-utils/v1/par" | python3 -c 'import json,sys; obj=json.load(sys.stdin); print(obj["response"])')
	response=`echo "$result" | cut -d '~' -f1`
	if [[ ${response} == "SUCCESS"* ]];then
		logMessagePatch "INFO" "Shared PAR URL fetched successfully." 
		PAR_URL=`echo "$result" | cut -d '~' -f2`
		PAR_URL="${PAR_URL}"
        mkdir -p cache
		kubectl -n ${SUB_NAMESPACE} get cm pipeline-config -ojson | jq -r '.data."pipeline.env"' > ./cache/pipeline.env
		sed -i '/PAR_URL/d' ./cache/pipeline.env
		echo -e -n "\nPAR_URL=\"${PAR_URL}\"" >> ./cache/pipeline.env
		sed -i '/^$/d' ./cache/pipeline.env
		kubectl -n ${SUB_NAMESPACE} create cm pipeline-config --from-file=./cache/pipeline.env -o yaml --dry-run=client | kubectl apply -f -
    else
		logMessagePatch "FATAL" "Failed to fetch shared PAR URL."
		echo "------------------"
		echo "$result"
		echo "------------------"
		exit -1
	fi
fi

logMessagePatch "INFO" "Picking schema screator image : $SCHEMACREATOR_SUFFIX"
logMessagePatch "INFO" "JOB_YAML : $JOB_YAML"
logMessagePatch "INFO" "JOB_NAME : $JOB_NAME"

sed -i \
    -e "s|{{PREFIX}}|${PREFIX}|g" \
	-e "s|{{REGISTRY}}|${REGISTRY}|g" \
	-e "s|__PATCH_ID_LOWER__|${patch_id_lowercase}|g" \
	-e "s|__SCHEMACREATOR_SUFFIX__|${SCHEMACREATOR_SUFFIX}|g" \
	-e "s|__PATCH_ID__|${PATCH_ID}|g" \
	-e "s|__TAG__|${PATCH_VERSION}|g" \
	-e "s|__TENANT_PDB_STRING__|None|g" \
	-e "s|__TENANT_ID__|${TENANT_ID}|g" \
	-e "s|__SERVICE_ID_LIST__|OFS_FSAFND~OFS_DFCS|g" \
	-e "s|__CURRENT_CLUSTER__|${MSP_PROJ_NAME^^}|g" \
	-e "s|__WTSS_URL__|http://${TENANT_ID}:9999|g" \
	-e "s|__DEFAULT_TIMEZONE__|${defaultTimeZone}|g" \
	-e "s|__CONFIG_SCHEMA_HOST__|${MSP_PROJ_NAME^^}|g" \
	-e "s|__PUSH_TO_SHARED_BUCKET__|false|g" \
	-e "s|__STREAM_POOL_ID__|${streampool_id}|g" \
	-e "s|__REGION__|${oci_region}|g" \
	-e "s|__CREATE_ORDER__|false|g" \
	-e "s|__BASE_SECRET_URLS__|${BASE_SECRET_URLS}|g" \
	-e "s|__REQUEST_ID__|${PATCH_ID}|g" \
	-e "s|__IS_ADW__|true|g" \
	-e "s|__SETUP_INFO_NAME__||g" \
	-e "s|__DISABLE_DEPENDENCIES__|true|g" \
	-e "s|__EXECUTE_CHANGELOGSYNC__|true|g" \
	./$JOB_YAML
	
cat ./$JOB_YAML

}

patch::runSCScripts() {

	patch::replaceSCParams
	logMessagePatch "INFO" "Running Schema Creator Execution job"
	
	logprefix=`logMessagePatch "INFO" ""`
	kubectl -n  "${SUB_NAMESPACE}" delete job $JOB_NAME --ignore-not-found --grace-period=0 --force 2>/dev/null
	k8s_create_job "${SUB_NAMESPACE}" "$JOB_NAME"  "$JOB_YAML"
	logMessagePatch "INFO" "Schema Creator execution job completed Successfully. "

}

#Start execution
patch::initialize
#if files exists then call schema creator
ACTIVE_FLAG=$(kubectl -n "${SUB_NAMESPACE}" get cm environment-status -o jsonpath=\{.data."ACTIVE_FLAG"\})
if [[ -z $ACTIVE_FLAG || "${ACTIVE_FLAG^^}" == "FALSE" ]];then
	logMessagePatch "INFO" "DR - Script Execution will be skipped, Exiting setup.."
	exit 0
fi

if [[ ${EXECUTE_SCRIPTS^^} == "TRUE" ]];then
	deploy::scripts
else
	logMessagePatch "INFO" "Script execution has not been enabled for this patch. Skipping the stage"
fi

if [[ ${SKIP_SC^^} != "TRUE" ]];then
	patch::runSCScripts
else
	logMessagePatch "INFO" "SchemaCreator execution has not been enabled for this patch. Skipping the stage"
	exit 0
fi

if [ $? -ne 0 ]; then
	echo "------------------------------------------------------------------------------------------------------------"
	logMessagePatch "FATAL" "${line} SCRIPT PATCH RETURNED FAILURE"
	echo "------------------------------------------------------------------------------------------------------------"
	exit -1
fi

if [[ $MSP_PROJ_NAME == "fsgbu-fsafnd" ]];then
	patch::setPatchSecret
fi	
