#!/bin/bash
chmod +x ./artstore
source ./common_functions.sh
source ./upgrade_and_patch_functions.sh
set -e
#REGION CODE LIST[ams, arn, auh, bom, cwl, dxb, fra, gru, hyd, iad, icn, jed, jnb, kix, lhr, lin, mel, mrs, mtz, nrt, phx, scl, sin, sjc, syd, vcp, yny, yul, yyz, zrh]
#TENANCY CODE LIST [dev, prod, devcorp]

export SUB_NAMESPACE={{COMMON_NAMESPACE}}--{{TENANT_ID}}
export MSP_PROJ_NAME={{MSP_PROJ_NAME}}
export CURRENT_CLUSTER={{MSP_PROJ_NAME}}
export PATCH_ID="dfcs"
export ARTPROJECT={{ARTPROJECT}}
export COMMON_NAMESPACE={{COMMON_NAMESPACE}}
export PATCH_PAYLOAD="payload.json"
export TENANCY={{TENANCY}}
export REGION_CODE={{REGION_CODE}}
export OST_ENVIRONMENT={{OST_ENVIRONMENT}}
export OST_CLASS={{OST_CLASS}}
export TENANT_ID={{TENANT_ID}}
export PATCH_STAGE={{PATCH_STAGE}}
export PATCH_VERSION={{PATCH_VERSION}}
export ARTSTORE_PATH={{ARTSTORE_PATH}}

export TENANT_STRING={{TENANT_STRING}}
export PATCH_SCRIPT={{PATCH_SCRIPT}}
export REGISTRY={{REGISTRY}}
export PREFIX={{PREFIX}}
export deployments_artifacts_path=dfcs-deployment-artifacts/$PATCH_VERSION
export ONBOARD_DIS_TENANT="{{ONBOARD_DIS_TENANT}}"
export CI_DIS_ARTIFACT_VERSION={{CI_DIS_ARTIFACT_VERSION}}  
export DIS_FORCE_DELETE={{DIS_FORCE_DELETE}}
DR={{DR}}
export DR=${DR,,}
export IS_MAINTENANCE_ENABLED="{{IS_MAINTENANCE_ENABLED}}"
export DV_ACTION="{{DV_ACTION}}"
export CI_PIPELINE_ID="{{CI_PIPELINE_ID}}"
export API_POLL_INTERVAL={{API_POLL_INTERVAL}}
export API_POLL_COUNT={{API_POLL_COUNT}}
export PATCH_ACTION={{PATCH_ACTION}}
#export NAMESPACE=$MSP_PROJ_NAME
logMessagePatch "INFO" "Executing in Sub-Namespace..."
logMessagePatch "INFO" "CI_PIPELINE_ID=$CI_PIPELINE_ID"
export PATCH_HOME=$(pwd)
#pre processing
chmod +x $PATCH_SCRIPT
. ./$PATCH_SCRIPT
#post processing
logMessagePatch "LOG" "Execution Complete in $MSP_PROJ_NAME namespace"
