#!/bin/bash

logMessagePatch(){
	Datelog=$(date +%Y-%m-%d)
  timestamplog=$(date +%H:%M:%S)
  Targethost=$HOSTNAME
  severity="$1"
  LoggerName="PATCH_UPGRADE"
  messageId="$3"
	messagebody="$2"
	if [[ ${severity^^} == "INFO" ]];then
		echo -e "\033[1;32m$Datelog $timestamplog -- $severity -- $messageId -- $messagebody\033[0m"
	elif [[ ${severity^^} == "WARN" || ${severity^^} == "WARNING" ]];then
		echo -e "\033[1;38;5;214m$Datelog $timestamplog -- $severity -- $messageId -- $messagebody\033[0m"
	elif [[ ${severity^^} == "ERROR" || ${severity^^} == "FATAL"  ]];then
		echo -e "\033[1;31m$Datelog $timestamplog -- $severity -- $messageId -- $messagebody\033[0m"
	else
		echo -e "$Datelog $timestamplog -- $severity -- $messageId -- $messagebody"
	fi
}

# Helper function to get the job pod name
get_job_pod() {
local sub_namespace="$1"
local component="$2"
job_pod=$(kubectl -n "${sub_namespace}" get pods --ignore-not-found=true --selector=job-name="${component}" -o=json | \
  jq -r '.items[] | select(.status.phase | test("Running|Succeeded|Failed") and .metadata.deletionTimestamp == null) | .metadata.name')
echo "$job_pod"
}

# Helper function to wait for a pod to reach a specific state
wait_for_pod_state() {
  local sub_namespace="$1"
  local podname="$2"
  local timeout_seconds="$3"
  local start_time=$(date +%s)

  while true; do
    pod_status=$(kubectl -n "${sub_namespace}" get pod "$podname" --ignore-not-found=true -o=json | jq -r 'select(.metadata.deletionTimestamp == null) | .status.phase' 2>/dev/null)
    case "$pod_status" in
      "Running"|"Succeeded")
        echo "$pod_status"
        break
        ;;
      "Failed")
        echo "Failed"
        break
        ;;
    esac

    current_time=$(date +%s)
    elapsed_time=$((current_time - start_time))
    if ((elapsed_time >= timeout_seconds)); then
      echo >&2 $(logMessagePatch "ERROR" "Timeout reached waiting for pod $podname to reach a valid state.")
      exit 1
    fi
    sleep 5
  done
}

# Helper function to wait for pod creation
wait_for_pod() {
  local sub_namespace="$1"
  local component="$2"
  local timeout_seconds="$3"
  local states="$4"
  local start_time=$(date +%s)

  while true; do
    podname=$(kubectl -n "${sub_namespace}" get pods --ignore-not-found=true --selector=job-name="${component}" -o=json | \
    jq -r --arg states "$states" '.items[] | select((.status.phase | test($states)) and .metadata.deletionTimestamp == null and 
    (.status.containerStatuses | all(.state.waiting.reason != "CrashLoopBackOff" and .state.waiting.reason != "CreateContainerConfigError"))) | .metadata.name' 2>/dev/null)
    if [ -n "$podname" ]; then
      echo "$podname"
      return 0
    fi

    current_time=$(date +%s)
    elapsed_time=$((current_time - start_time))
    if ((elapsed_time >= timeout_seconds)); then
      echo >&2 $(logMessagePatch "ERROR" "Timeout reached waiting for pod for job ${component}.")
      return 1
    fi
    sleep 5
  done
}

k8s_create_job() {
  local sub_namespace="$1"
  local component="$2"
  local filename="$3"
  local pod_timeout_seconds=360 # 6 minutes
  local start_time=$(date +%s)
  local job_timeout_seconds=4500 # 1hour 15mins

  logprefix=$(logMessagePatch "INFO" "")
  kubectl -n "${sub_namespace}" apply -f "${filename}" | sed "s/^/${logprefix}/"

  # Wait for pod creation
  logMessagePatch "LOG" "Waiting to create the pod for job ${component}."
  podname=$(wait_for_pod "${sub_namespace}" "${component}" "${pod_timeout_seconds}" "Running|Succeeded")
  if [ -z "$podname" ]; then
    logMessagePatch "ERROR" "Failed to find a valid pod for component ${component} within timeout."
    exit 1
  fi

  # Wait for pod to reach running or succeeded state
  pod_status=$(wait_for_pod_state "${sub_namespace}" "$podname" "${pod_timeout_seconds}")
  if [[ "$pod_status" == "Failed" ]]; then
    logMessagePatch "ERROR" "Pod $podname failed to start. Exiting."
    #kubectl -n "${sub_namespace}" describe pod "$podname"
    exit 1
  fi

  logMessagePatch "INFO" "Pod name for Job - $component is - $podname"
  kubectl -n "${sub_namespace}" logs -f "$podname" & LOG_PID=$!
  wait_for_job "$job_timeout_seconds" "${component}" "${sub_namespace}" "$podname" "$LOG_PID"
}

wait_for_job() {
  local job_timeout_seconds="$1"
  local component="$2"
  local sub_namespace="$3"
  local podname="$4"
  local log_pid="$5"
  
  local start_time=$(date +%s)

  #logMessagePatch "LOG" "Waiting for job ${component} to complete."
  set +e
  while true; do
    current_time=$(date +%s)
    elapsed_time=$((current_time - start_time))

    # Check for timeout
    if ((elapsed_time >= job_timeout_seconds)); then
      logMessagePatch "FATAL" "Timeout exceeded while waiting for job ${component} in namespace ${sub_namespace}."
      { [[ -n "$log_pid" ]] && kill "$log_pid" 2>/dev/null; } &>/dev/null
      kubectl -n "${sub_namespace}" describe pod "$podname"
      exit 1
    fi

    # Check if the original pod is still alive
    pod_status=$(kubectl -n "${sub_namespace}" get pod "$podname" --ignore-not-found=true -o=jsonpath='{.status.phase}')
    if [ -z "$pod_status" ]; then
      logMessagePatch "WARN" "Pod - $podname is no longer running. Checking for new pod."

      # Look for a new pod associated with the job
      new_pod=$(get_job_pod "${sub_namespace}" "${component}")
      if [ -n "$new_pod" ]; then
        logMessagePatch "LOG" "New pod - $new_pod created. Checking status."
        pod_status=$(wait_for_pod_state "${sub_namespace}" "$new_pod" "360")

        case "$pod_status" in
          "Running")
            logMessagePatch "LOG" "New pod $new_pod is running. Fetching logs."
            kubectl -n "${sub_namespace}" logs -f "$new_pod" & log_pid=$!
            podname="$new_pod" # Update the podname
            ;;
          "Succeeded")
            logMessagePatch "INFO" "Job ${component} completed successfully with new pod $new_pod."
            kubectl -n "${sub_namespace}" logs "$new_pod"
            #{ [[ -n "$log_pid" ]] && kill "$log_pid" 2>/dev/null; } &>/dev/null
            set -e
            break
            ;;
          "Failed")
            logMessagePatch "ERROR" "Job ${component} failed with new pod $new_pod."
            #kubectl -n "${sub_namespace}" describe pod "$new_pod"
            #{ [[ -n "$log_pid" ]] && kill "$log_pid" 2>/dev/null; } &>/dev/null
            set -e
            exit 1
            ;;
        esac
      else
        logMessagePatch "ERROR" "No new pod found for job ${component}. Exiting."
        exit 1
      fi
    else
      # Handle the case where the original pod is still running or has completed
      case "$pod_status" in
        "Succeeded")
          logMessagePatch "INFO" "Job completed successfully pod - $podname."
          #{ [[ -n "$log_pid" ]] && kill "$log_pid" 2>/dev/null; } &>/dev/null
          set -e
          break
          ;;
        "Failed")
          logMessagePatch "ERROR" "Job failed, pod - $podname."
          #{ [[ -n "$log_pid" ]] && kill "$log_pid" 2>/dev/null; } &>/dev/null
          set -e
          exit 1
          ;;
      esac
    fi

    sleep 10
  done
}

replace_aai_image_tag_versions(){
	set +e
	component="$1"
	props_file="$2"
	sync_file="$3"
	patterns=""
	if [ -f "$2" ];then
		echo -e "\n" >> "$2" 
		while read -r line; do 
			name=$(echo $line | cut -d '=' -f1)
			tag=$(echo $line | cut -d '=' -f2)
			if [ ! -z ${name} ];then
				patterns="${patterns} s/${name}:__TAG__/${name}:${tag}/g;"
			fi
		done < "$2"

		if [ -d "$1" ];then
			find "$1" -type f \( -iname \*.yaml.tpl -o -iname \*.yml.tpl -o -iname \*.yaml -o -iname \*.yml \) | while read fname; do sed -i "${patterns}" $fname; done;
		fi
		if [ -f "$3" ];then
			sed -i "${patterns}" "$3"
		fi
	else
		echo "$2 file does not exist. Exiting..."
		exit 1
	fi
	set -e
}

_idcs_groups_create() {
  local token="$1"
  local json_list="$2"
  if [ ! -f $json_list ];then
	logMessagePatch "INFO" "Files doesn't exist $json_list. Skipping this file !!!"
  else
	key_pair=$(cat $json_list)
	payload="group.json"
	while read -r line
	do
		group_id=$(echo $line | cut -d ':' -f1 | tr -d '\n' | tr -d '\r')
		group_name=$(echo $line | cut -d ':' -f2 | tr -d '\n' | tr -d '\r')
		echo "group_id=$group_id , group_name=$group_name"
		cat > $payload << __EOF__
{
    "displayName": "$group_id",
    "urn:ietf:params:scim:schemas:oracle:idcs:extension:group:Group": {
        "creationMechanism": "api",
        "description": "$group_name"
    },
    "schemas": [
        "urn:ietf:params:scim:schemas:core:2.0:Group",
        "urn:ietf:params:scim:schemas:oracle:idcs:extension:group:Group"
    ]
}
__EOF__
		
		WTSS_IDCS_HOST=$(kubectl -n ${SUB_NAMESPACE} get secret tenant-order -o jsonpath=\{.data."WTSS_IDCS_HOST"\} | base64 -d)
		result=$(curl -X POST -H "Content-Type:application/json" -H "Authorization: Bearer $token" --data @"${payload}" ${WTSS_IDCS_HOST}/admin/v1/Groups -o group_output.txt --silent --write-out "%{http_code}")
		if [[ "$result" = "409" ]]; then
        logMessagePatch "INFO" "$group_id Group already exists."
    elif [[ "$result" = "201" ]]; then
        cat group_output.txt
        logMessagePatch "INFO" "New Group $group_id added."
    else
        cat group_output.txt
    fi
    logMessagePatch "INFO" "$group_id : $result"
	done <<< "$key_pair"
	logMessagePatch "INFO" "Group creation completed"
  fi
}

_get_idcs_token(){
	IDCS_CLIENT_ID=$(kubectl -n ${SUB_NAMESPACE} get secret tenant-order -o jsonpath=\{.data."IDCS_CLIENT_ID"\} | base64 -d)
	IDCS_CLIENT_SECRET=$(kubectl -n ${SUB_NAMESPACE} get secret tenant-order -o jsonpath=\{.data."IDCS_CLIENT_SECRET"\} | base64 -d)
	WTSS_IDCS_HOST=$(kubectl -n ${SUB_NAMESPACE} get secret tenant-order -o jsonpath=\{.data."WTSS_IDCS_HOST"\} | base64 -d)
	encoded=`echo -n "${IDCS_CLIENT_ID}:${IDCS_CLIENT_SECRET}" | base64 -w 0`
    ACCESS_TOKEN=$(curl -k -s -X POST -H "Authorization: Basic $encoded" -d 'grant_type=client_credentials&scope=urn:opc:idm:__myscopes__' "${WTSS_IDCS_HOST}/oauth2/v1/token")
    #token=$(echo ${ACCESS_TOKEN} | python -c 'import sys, json; print json.load(sys.stdin)[sys.argv[1]]' access_token)
	token=$(echo ${ACCESS_TOKEN} | python3 -c 'import sys, json; print(json.loads(sys.stdin.read())[sys.argv[1]])' access_token)
	echo "$token"
}

_getIDCSAppDetails(){
	url="$1"
	token="$2"
	displayName="$3"	
	details=$(curl -s -X POST \
	"$url" \
	-H "Content-Type: application/json" \
	-H "Authorization: Bearer $token" \
	-d '{
		 "schemas": ["urn:ietf:params:scim:api:messages:2.0:SearchRequest"],
		 "filter":
		   "displayName eq \"'$displayName'\""
		}')
  echo "$details"
}

vercomp() {
		if [[ $1 == $2 ]];then
		echo 0
	fi    
    local IFS=.
    local i ver1=($1) ver2=($2)
    # fill empty fields in ver1 with zeros
    for ((i=${#ver1[@]}; i<${#ver2[@]}; i++))
    do
        ver1[i]=0
    done
    for ((i=0; i<${#ver1[@]}; i++))
    do
        if [[ -z ${ver2[i]} ]]
        then
            # fill empty fields in ver2 with zeros
            ver2[i]=0
        fi
        if ((10#${ver1[i]} > 10#${ver2[i]}))
        then
            echo 1
			break
        fi
        if ((10#${ver1[i]} < 10#${ver2[i]}))
        then
            echo 2
			break
        fi
    done
}

versionCheck () {
	res=$(vercomp $1 $3)
	if [[ $res -eq 0 ]];then
		op="eq"
	elif [[ $res -eq 1 ]];then
		op='gt'
	elif [[ $res -eq 2 ]];then
		op='lt'
	fi
	if [[ ( "$2" == "ge") && ( $op == "eq" || $op == "gt" ) ]];then
		echo true
	elif [[ ("$2" == "le") && ($op == "eq" || $op == "lt") ]];then
		echo true
	elif [[ ("$2" == "gt") && ($op == "gt") ]];then
		echo true
	elif [[ ("$2" == "lt") && ($op == "lt") ]];then
		echo true
	elif [[ ("$2" == "eq") && ("$op" == "eq") ]];then
		echo true
	elif [[ ("$2" == "ne") && ("$op" == "gt" || "$op" == "lt") ]];then
		echo true
	else
		echo false
	fi		
}
fileContains(){
	searchText="$1"
	searchFile="$2"
	if [[ -s "$searchFile" ]];then
		if grep -q "$searchText" "$searchFile"; then
			echo "true"
		else
			echo "false"
		fi
	else
		echo "false"
	fi
}

# Function to make API requests
make_api_request() {
    local url_path="$1"
    local method="${2:-GET}"
    local payload="${3:-}"
    local client_id="${4:-}"
    local client_secret="${5:-}"
    local custom_headers="${6:-}"
    local api_poll_count="${7:-3}"
    local api_poll_interval="${8:-5}"

    # Default headers
    local headers=("Content-Type: application/json")
    local auth_header=""

    # Add Basic Authentication header if client_id and client_secret are provided
    if [[ -n "$client_id" && -n "$client_secret" ]]; then
        auth_header="Authorization: Basic $(echo -n "$client_id:$client_secret" | base64)"
        headers+=("$auth_header")
    fi

    # Add custom headers if provided
    if [[ -n "$custom_headers" ]]; then
        IFS=";" read -ra custom_headers_array <<< "$custom_headers"
        for header in "${custom_headers_array[@]}"; do
            headers+=("$header")
        done
    fi

    # Retry loop
    for ((attempt=1; attempt<=api_poll_count; attempt++)); do
        echo >&2 $(logMessagePatch "LOG" "Attempt $attempt: Making $method Request...")

        # Determine Content-Type and payload format
        local curl_opts=()
        if [[ "$method" == "POST" || "$method" == "PUT" ]]; then
            if [[ "${headers[*]}" == *"application/x-www-form-urlencoded"* ]]; then
                curl_opts+=("--data-urlencode" "$payload")
            else
                curl_opts+=("--data" "$payload")
            fi
        fi

        # Set the HTTP method
        curl_opts+=("-X" "$method")

        # Add headers
        for header in "${headers[@]}"; do
            curl_opts+=("-H" "$header")
        done

        # Make the API call
        local response
        response=$(curl -sS -k "${curl_opts[@]}" "$url_path" --write-out "HTTPSTATUS:%{http_code}" || true)
        local http_status=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
        local response_body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//')

        # Check HTTP status
        if [[ "$http_status" -ge 200 && "$http_status" -lt 300 || "$http_status" -eq 503 ]]; then
            echo "$response_body"
            return 0
        elif [[ "$http_status" -ge 500 ]]; then
            echo >&2 "Server error ($http_status). Retrying in $api_poll_interval seconds..."
        elif [[ "$http_status" -ge 400 && "$http_status" -lt 500 ]]; then
            echo >&2 "Client error ($http_status). Response: $response_body"
            return 1
        else
            echo >&2 "Unexpected HTTP status ($http_status). Retrying..."
        fi

        # If not the last attempt, wait before retrying
        if [[ $attempt -lt $api_poll_count ]]; then
            sleep "$api_poll_interval"
        fi
    done

    echo >&2 $(logMessagePatch "LOG" "Exceeded maximum retry attempts.")
    return 1
}


get_fsafnd_service_url(){
	tenant_id="$1"
	service_key="$2"
	service_url=$(kubectl -n fsgbu-fsafnd--${tenant_id} get secrets service-properties -o jsonpath='{.data.service-properties\.props}' | base64 -d | grep -w $service_key | cut -d '=' -f2)
	echo "$service_url"
	return 0
}

get_service_and_data() {
    local service_config="$1"  
    local container_name="$2" 
    local result
    result=$(echo "$service_config" | jq -r \
        --arg container "$container_name" \
        '. as $root | keys[] as $service | $service as $svc | $root[$svc][] | select(has($container)) | $svc + ":" + .[$container]' | sed 's/^ *//;s/ *$//')

    if [[ -n "$result" ]]; then
        echo "$result"  # Output is in the format- "service_name:data_value"
    else
        echo "NOT_FOUND"
    fi
}


k8s_create_deployment_job() {
  local sub_namespace="$1"
  local component="$2"
  local filename="$3"
  logprefix=`logMessagePatch "INFO" ""`
  kubectl -n "${sub_namespace}" apply -f "${filename}" | sed "s/^/${logprefix}/"
  # wait till the job is complete
  wait_for_deployment_job "${component}" "${sub_namespace}"
}


wait_for_deployment_job() {
  local component=$1
  local sub_namespace=$2
  countdown=1
  wait_for_job_timeout=${wait_for_job_timeout:-10800} #Wait time : 3hrs
  echo "job timeout is : $wait_for_job_timeout"
  timeout=${wait_for_job_timeout}
  while [ ${countdown} -gt 0 ]
  do
		(( countdown=countdown+5 ))
        if [ $countdown -gt $timeout ]
        then
            logMessagePatch "FATAL" "Timeout exceeded while waiting for job in $sub_namespace!"
			echo -e -n "\nFATAL_MESSAGE='92-${component}-failed-Timeout-exceeded'" >> tenant.env
			set +e
			kubectl -n ${sub_namespace}  get pods --selector=job-name="${component}" --output=jsonpath='{.items[0].metadata.name}'
			if [ $? -eq 0 ];then
				podname=$(kubectl -n ${sub_namespace}  get pods --selector=job-name="${component}" --output=jsonpath='{.items[0].metadata.name}')
				if [ ! -z "${podname}" ];then
					kubectl -n ${sub_namespace} describe pod ${podname}
					kubectl -n ${sub_namespace} logs ${podname}
				else
					kubectl -n ${sub_namespace} get events
				fi
			else
				kubectl -n ${sub_namespace} get events
			fi
            exit 1
        fi
        sleep 5s
		job_pod=$(kubectl -n ${sub_namespace} get pods -o name | grep ${component})
		if [[ ! -z $job_pod ]]; then
			result=$(kubectl -n ${sub_namespace}  get pods --selector=job-name="${component}" --output=jsonpath='{.items[0].status.phase}')
			if [ ! -z ${result} ]; then
				if [[ "$result" == "Succeeded" ]];then
					podname=$(kubectl -n ${sub_namespace}  get pods --selector=job-name="${component}" --output=jsonpath='{.items[0].metadata.name}')
					echo "podname is : $podname"					
					echo "Execution logs for sub_namespace ${sub_namespace}"
					echo "Pod logs as below - podname-$podname"
					kubectl -n ${sub_namespace} logs $podname
					logMessagePatch "INFO" "${component} execution was successful"
					echo "-----------------------------"
					set -e
					break;
				elif [[ "$result" == "Failed" ]]; then
					podname=$(kubectl -n ${sub_namespace}  get pods --selector=job-name="${component}" --output=jsonpath='{.items[0].metadata.name}')
					echo "podname is : $podname"
					echo "Pod logs as below - podname-$podname"
					kubectl -n ${sub_namespace} logs $podname
					logMessagePatch "ERROR" "${component} execution failed."
					echo "-----------------------------"
					set -e
					exit -1
				fi
			fi
		fi
   done
}
