#!/bin/bash
./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -rf $deployments_artifacts_path/env/$MSP_PROJ_NAME/pipeline-$OST_ENVIRONMENT.env ./env/$MSP_PROJ_NAME/ -e k8s	
mv ./env/$MSP_PROJ_NAME/pipeline-$OST_ENVIRONMENT.env pipeline.env
source ./pipeline.env

replaceParams(){
	
	sed -i \
			-e "s|{{PREFIX}}|${PREFIX}|g" \
			-e "s|{{REGISTRY}}|${REGISTRY}|g" \
			-e "s|__artstore_path__|${ARTSTORE_PATH}|g" \
			-e "s|__deployments_artifacts_path__|${deployments_artifacts_path}|g" \
			-e "s|__OST_ENVIRONMENT__|${OST_ENVIRONMENT}|g" \
			-e "s|__PATCH_STAGE__|${PATCH_STAGE}|g" \
			-e "s|__PATCH_ID__|${PATCH_ID}|g" \
			-e "s|__REGION__|${oci_region}|g" \
			-e "s|__REGION_CODE__|${REGION_CODE}|g" \
			-e "s|__TENANCY__|${TENANCY}|g" \
			-e "s|__WALLET_PATH__|${BASE_SECRET_URLS}${TENANT_ID}|g" \
			-e "s|__TENANT_ID__|${TENANT_ID}|g" \
			-e "s|__BASE_SECRET_URLS__|${BASE_SECRET_URLS}|g" \
			-e "s|__ADW__|true|g" \
			-e "s|__job_exec_name__|aai-frc-upgrade|g" \
			-e "s|__CI_PIPELINE_ID__|${CI_PIPELINE_ID}|g" \
		-e "s|__API_POLL_COUNT__|${API_POLL_COUNT}|g" \
		-e "s|__PATCH_VERSION__|${PATCH_VERSION}|g" \
			-e "s|__API_POLL_INTERVAL__|${API_POLL_INTERVAL}|g" \
			-e "s|__MSP_PROJ_NAME__|${MSP_PROJ_NAME}|g" \
			./patch_job_handler.yaml.tpl
	
	mv patch_job_handler.yaml.tpl aai-frc-upgrade-job.yaml
	cat aai-frc-upgrade-job.yaml

}
deploy_aai_upgrade_job(){
	
	chmod +x aai-frc-upgrade-job.sh
	logprefix=`logMessagePatch "INFO" ""`
	replaceParams
	kubectl -n  "${SUB_NAMESPACE}" delete job aai-frc-upgrade-exec --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" delete configmap aai-frc-upgrade-job --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" create configmap aai-frc-upgrade-job --from-file=aai-frc-upgrade-job.sh
	k8s_create_job "${SUB_NAMESPACE}" "aai-frc-upgrade-exec" "aai-frc-upgrade-job.yaml"
	result=$(kubectl -n ${SUB_NAMESPACE}  get pods --selector=job-name="aai-frc-upgrade-exec" --output=jsonpath='{.items[0].status.phase}')
	if [[ "${result^^}" == "FAILED" ]]; then
		exit -1
	fi
	
}

#Start
patch::initialize
./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -rf $deployments_artifacts_path/aai_image_versions.props ./ -e k8s
export $(grep '^aai_artfacts_version=' aai_image_versions.props | xargs)	
export $(grep '^aai_artfacts_path=' aai_image_versions.props | xargs)
./artstore -p fsgbu-fsafnd-shared -g ${REGION_CODE} -n ${TENANCY}  file pull -rf ${aai_artfacts_path}/${aai_artfacts_version} ./ -e k8s	
find . -type f -name "*.sh" -exec chmod 775 {} +

if [[ "${DEPLOY_AAI^^}" == "TRUE" ]];then
        if [[ $PATCH_ACTION == "PRE_UPGRADE" ]];then
                logMessagePatch "INFO" "Pre Upgrade triggered.."
                ./pre-aai-deploy/setup.sh
                if [ $? -ne 0 ]; then
                        logMessagePatch "ERROR" "Pre Upgrade failed in $NAMESPACE."
                        exit -1
                fi
        elif [[ $PATCH_ACTION == "POST_UPGRADE" ]];then
                logMessagePatch "INFO" "Post Upgrade triggered.."
                ./post-aai-deploy/setup.sh
                if [ $? -ne 0 ]; then
                        logMessagePatch "ERROR" "Post Upgrade failed in $NAMESPACE."
                        exit -1
                fi
        elif [[ $PATCH_ACTION == "FRC_UPGRADE" ]];then
                logMessagePatch "INFO" "FRC UPGRADE triggered.."
                deploy_aai_upgrade_job
                logMessagePatch "INFO" "AAI FRC UPGRADE job Successfully Applied."
        fi
else
        logMessagePatch "INFO" "Deploy AAI is disabled for this patch."
fi

