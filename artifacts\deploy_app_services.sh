#!/bin/bash

#set ${DEBUG:-+x}
chmod +x ./artstore
patch::initialize
#set +i
./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -f $deployments_artifacts_path ./ -e k8s
replace_app_image_tag_versions

if [[ $MSP_PROJ_NAME == "fsgbu-fsafnd" ]];then	
	# token=$(_get_idcs_token)
	# if [[ -z "$token" ]];then
	# 	echo "Token is empty."
	# fi
	# echo "USER_GROUPS_CREATION Flag : $USER_GROUPS_CREATION" 
	# if [[ -z $USER_GROUPS_CREATION || $USER_GROUPS_CREATION == "true" ]];then
	#   _idcs_groups_create "$token" "dfcs-services-grp.txt"
	# else
	#   logMessagePatch "INFO" "New groups has not been added for DFCS Application hence Skipping the stage"
	# fi
	
	dep_file_names=$FSAFND_APP_DEPLOYMENT_NAMES	
	svc_file_names=$FSAFND_APP_SVC_NAMES
	echo "svc_file_names=$svc_file_names"
	echo "$MSP_PROJ_NAME : dep_file_names - $dep_file_names"
	if [[ -z "$dep_file_names" && -z "$svc_file_names" ]];then
		logMessagePatch "INFO" "App services deployment for $MSP_PROJ_NAME is not enabled for this patch, Skipping the stage"
		exit 0
	fi

elif [[ $COMMON_NAMESPACE == "fsgbu-dfcs" ]];then	
	svc_file_names=$DFCS_APP_SVC_NAMES
	dep_file_names=$DFCS_APP_DEPLOYMENT_NAMES
	echo "svc_file_names=$svc_file_names"
	echo "dep_file_names=$dep_file_names"
	
	if [[ -z "$dep_file_names" && -z "$svc_file_names" ]];then
		logMessagePatch "INFO" "App services deployment for $MSP_PROJ_NAME is not enabled for this patch, Skipping the stage"
		exit 0
	fi	
fi

for i in ${svc_file_names//,/ }
	do
		kubectl -n ${SUB_NAMESPACE} apply -f $i
		logMessagePatch "INFO" "Service - $i refreshed successfully."
	done

for i in ${dep_file_names//,/ }
	do
	    patch::replace_placeholders "$i" "false"

		if [ "$i" = "common-dm.dep.yaml" ]; then
			dp_image_version=$(cat fsafnd_image_versions.props | grep "dpdeploymentimg" | cut -d '=' -f2)
			catalog_content_version=$(cat fsafnd_image_versions.props | grep "catalog-content-version=" | cut -d '=' -f2)
			sed -i \
					-e "s|__DEPLOYMENT_JOB_VERSION__|$dp_image_version|g" \
					-e "s|__CONTENT_VERSION__|$catalog_content_version|g" \
					-e "s|__CONTENT_ZIP_NAME__|dfcs-$catalog_content_version.zip|g" \
			$i
			cat $i
		fi
		
		deploymentname=`sed -n '/metadata:/,/spec:/p' $i | grep -oP '(?<=name: ).*' | tr '\n' ' ' |  cut -d " " -f1`
		echo "DeploymentName: ${deploymentname}"
		kubectl -n ${SUB_NAMESPACE} delete deployment $deploymentname --ignore-not-found		
		kubectl -n ${SUB_NAMESPACE} apply -f $i
		logMessagePatch "INFO" "Deployment- $deploymentname refreshed successfully."
	done

logMessagePatch "INFO" "Deployment complete for ${SUB_NAMESPACE}"
