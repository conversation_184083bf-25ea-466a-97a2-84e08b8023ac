#!/bin/bash

#set ${DEBUG:-+x}
chmod +x ./artstore
patch::initialize
if [[ -z "$DEPLOY_FSS_SERVICES" || "$DEPLOY_FSS_SERVICES" == "false" ]];then
logMessagePatch "INFO" "Common FSS Services deployment is not enabled for this patch. Skipping the stage"
exit 0
fi

replace_placeholders(){
dirctry="${1:-./}"
find "$dirctry" -type f \( -iname \*.yaml \) | while read fname; do patch::replace_placeholders "$fname" "false"; done;
}
./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -f $deployments_artifacts_path ./ -e k8s
replace_placeholders
replace_app_image_tag_versions
if [[ $MSP_PROJ_NAME == "fsgbu-fsafnd" ]];then	
	kubectl -n "${SUB_NAMESPACE}" delete cm filestream-run-config --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" delete cm filestore-run-config --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" delete cm fss-container-config --ignore-not-found
	kubectl -n ${SUB_NAMESPACE} delete deployments.apps common-fss-api --ignore-not-found
	kubectl -n ${SUB_NAMESPACE} delete StatefulSet common-fss-utils common-fss-kafka --ignore-not-found
	kubectl -n ${SUB_NAMESPACE} delete svc akka-seed-service akka-seed --ignore-not-found
	if [[ ${OST_CLASS^^} == "PROD" ]];then
		kubectl -n ${SUB_NAMESPACE} apply -f common-fss-config.cm-prod.yaml
	else
		kubectl -n ${SUB_NAMESPACE} apply -f common-fss-config.cm.yaml
	fi	
	kubectl -n ${SUB_NAMESPACE} apply -f filestream-run-cm.yaml
	kubectl -n ${SUB_NAMESPACE} apply -f filestore-run-cm.yaml
	kubectl -n ${SUB_NAMESPACE} apply -f common-fss-utils.svc.yaml

	kubectl -n ${SUB_NAMESPACE} apply -f common-fss-utils.dep.yaml
	kubectl -n ${SUB_NAMESPACE} apply -f common-fss-kafka-dep.yaml
	sleep 120s
	kubectl -n ${SUB_NAMESPACE} apply -f common-fss-api.dep.yaml
	sleep 60s	
	logMessagePatch "INFO" "sfs deploy complete.."
fi
	

