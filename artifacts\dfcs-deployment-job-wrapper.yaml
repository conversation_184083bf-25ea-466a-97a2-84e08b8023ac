apiVersion: batch/v1
kind: Job
metadata:
  name: dfcs-deployment-exec-job
spec:
  backoffLimit: 1
  ttlSecondsAfterFinished: 200
  template:
    metadata:
      annotations:
        version: v1.0
      labels:
        component: dfcs-deployment-exec-job
    spec:
      containers:
        - image: {{REGISTRY}}/{{PREFIX}}-fsgbu-fsafnd-shared/client-base-image:22.07.03
          name: dfcs-deployment-exec-job
          imagePullPolicy: Always
          env:
            - name: TENANCY
              value: "__TENANCY__"
            - name: REGION_CODE
              value: "__REGION_CODE__"
            - name: DEP_ARTIFACTS_PATH
              value: "__deployments_artifacts_path__"
            - name: MSP_PROJ_NAME
              value: "__MSP_PROJ_NAME__"
            - name: PATCH_STAGE
              value: "__PATCH_STAGE__"
            - name: ARTSTORE_PATH
              value: "__artstore_path__"
            - name: WALLET_PATH
              value: "__WALLET_PATH__"
            - name: DB_ALIAS
              valueFrom:
                secretKeyRef:
                  name: dbaas-details
                  key: DB_ALIAS
            - name: TASK_NAME
              value: "__TASK_NAME__"            
          command:
            - 'sh'
            - '-c'
            - '/home/<USER>/dfcs_deployment_job_wrapper.sh'
          volumeMounts:            
            - name: exec-volume
              mountPath: /home/<USER>/dfcs_deployment_job_wrapper.sh
              subPath: dfcs_deployment_job_wrapper.sh
            - name: oci-vol
              mountPath: /os_parameters
            - name: sat-vol
              mountPath: /opt/sat/token
              subPath: sat
      restartPolicy: Never
      serviceAccountName: namespace-admin
      securityContext:
        runAsUser: 1000
      volumes:
        - name: exec-volume
          configMap:
            defaultMode: 0777
            name: dfcs-deployment-job-wrapper
        - name: oci-vol
          secret:
            secretName: reserved-object-store-entity
        - name: sat-vol
          secret:
            secretName: reserved-service-access-token
