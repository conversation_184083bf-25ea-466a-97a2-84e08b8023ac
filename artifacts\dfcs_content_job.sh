#!/bin/bash
chmod +x ./artstore
chmod +x ./common_functions.sh
source ./common_functions.sh


dfcs_content_job_exec() {

    set -e
    logMessagePatch "INFO" "Executing data platform job..."
    logMessagePatch "INFO" "Getting files from artstore...."
    ./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -rf $deployments_artifacts_path ./ -e k8s
 
    logMessagePatch "INFO" "Getting props file..."
	
	if [[ -f "fsafnd_image_versions.props" ]];then
		logMessagePatch "INFO" "fsafnd_image_versions.props present"
		image_version=$(cat fsafnd_image_versions.props | grep "dfcs-content-job" | cut -d '=' -f2)
	else
		logMessagePatch "FATAL" "fsafnd_image_versions.props not present"
        exit 0
	fi

    if [[ -f "dfcs-content-job.yaml" ]]; then
        logMessagePatch "INFO" "Replacing placeholders in dfcs-content-job.yaml file..."
		patch::replace_placeholders "dfcs-content-job.yaml" "false"
		
	    sed -i \
			-e "s|__TAG__|$image_version|g" \
			dfcs-content-job.yaml
    else
        logMessagePatch "FATAL" "dfcs-content-job.yaml file not present"
        exit 0
    fi

    # job name
    component="dfcs-content-job"

    cat dfcs-content-job.yaml

    echo -e "\n Checking if $component kubernetes job is already present"
    if kubectl get jobs -n ${SUB_NAMESPACE} | grep -qw ${component}; then
        logMessagePatch "INFO" "Job ${component} exists."
        logMessagePatch "INFO" "deleting $component kubernetes job"
	    kubectl -n ${SUB_NAMESPACE} delete jobs ${component}
	    logMessagePatch "INFO" "deleted $component kubernetes job successfully"
    else
        logMessagePatch "INFO" "Job ${component} does not exist."
    fi

	logMessagePatch "INFO" "Executing $component kubernetes job"
    k8s_create_job "${SUB_NAMESPACE}" "${component}" dfcs-content-job.yaml
    logMessagePatch "INFO" "kubernetes $component job has been executed successfully"
}

#Execution Starts 
patch::initialize
dfcs_content_job_exec
