#!/bin/bash
set -e

chmod +x ./artstore
chmod +x ./common_functions.sh
source ./common_functions.sh

replaceParams(){
    taskName=${1}
	echo "Task Name : $taskName"
sed -i \
        -e "s|{{PREFIX}}|${PREFIX}|g" \
        -e "s|{{REGISTRY}}|${REGISTRY}|g" \
        -e "s|__artstore_path__|${ARTSTORE_PATH}|g" \
        -e "s|__deployments_artifacts_path__|${deployments_artifacts_path}|g" \
        -e "s|__PATCH_STAGE__|${PATCH_STAGE}|g" \
        -e "s|__PATCH_ID__|${PATCH_ID}|g" \
        -e "s|__REGION__|${oci_region}|g" \
        -e "s|__REGION_CODE__|${REGION_CODE}|g" \
        -e "s|__TENANCY__|${TENANCY}|g" \
        -e "s|__WALLET_PATH__|${BASE_SECRET_URLS}${TENANT_ID}|g" \
        -e "s|__TENANT_ID__|${TENANT_ID}|g" \
        -e "s|__BASE_SECRET_URLS__|${BASE_SECRET_URLS}|g" \
        -e "s|__ADW__|true|g" \
        -e "s|__MSP_PROJ_NAME__|${MSP_PROJ_NAME}|g" \
		-e "s|__TASK_NAME__|${taskName}|g" \
        ./dfcs-deployment-job-wrapper.yaml
		
		cat ./dfcs-deployment-job-wrapper.yaml

}

revertTaskName(){
    taskName=${1}
	echo "Reverting Task Name : $taskName"
sed -i \
		-e "s|${taskName}|__TASK_NAME__|g" \
        ./dfcs-deployment-job-wrapper.yaml
		
		cat ./dfcs-deployment-job-wrapper.yaml
}

deployment_upgrading_flag_set(){
	chmod +x dfcs_deployment_job_wrapper.sh
	logprefix=`logMessagePatch "INFO" ""`
	replaceParams "SET_UPGRADED_FLAG"
	echo "sub_namespace----- : ${SUB_NAMESPACE}"
	kubectl -n  "${SUB_NAMESPACE}" delete job dfcs-deployment-exec-job --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" delete configmap dfcs-deployment-job-wrapper --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" create configmap dfcs-deployment-job-wrapper --from-file=dfcs_deployment_job_wrapper.sh
	k8s_create_job "${SUB_NAMESPACE}" "dfcs-deployment-exec-job" "dfcs-deployment-job-wrapper.yaml"
	result=$(kubectl -n ${SUB_NAMESPACE}  get pods --selector=job-name="dfcs-deployment-exec-job" --output=jsonpath='{.items[0].status.phase}')
	if [[ "${result^^}" == "FAILED" ]]; then
		exit -1
	fi
	revertTaskName "SET_UPGRADED_FLAG"
}

deployment_exec_check(){
	chmod +x dfcs_deployment_job_wrapper.sh
	logprefix=`logMessagePatch "INFO" ""`
	replaceParams "CHECK_DEPLOYMENT_STEPS"
	echo "sub_namespace----- : ${SUB_NAMESPACE}"
	kubectl -n  "${SUB_NAMESPACE}" delete job dfcs-deployment-exec-job --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" delete configmap dfcs-deployment-job-wrapper --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" create configmap dfcs-deployment-job-wrapper --from-file=dfcs_deployment_job_wrapper.sh
	k8s_create_job "${SUB_NAMESPACE}" "dfcs-deployment-exec-job" "dfcs-deployment-job-wrapper.yaml"
	result=$(kubectl -n ${SUB_NAMESPACE}  get pods --selector=job-name="dfcs-deployment-exec-job" --output=jsonpath='{.items[0].status.phase}')
	if [[ "${result^^}" == "FAILED" ]]; then
		exit -1
	fi
	revertTaskName "CHECK_DEPLOYMENT_STEPS"
}


dfcs_deployment_job_exec() {

    set -e
    logMessagePatch "INFO" "Executing dfcs deployment job..."
    logMessagePatch "INFO" "Getting files from artstore...."
    ./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -rf $deployments_artifacts_path ./ -e k8s
 
    logMessagePatch "INFO" "Getting props file..."
	
	if [[ -f "fsafnd_image_versions.props" ]];then
		logMessagePatch "INFO" "fsafnd_image_versions.props present"
		image_version=$(cat fsafnd_image_versions.props | grep "dfcs-deployment-job" | cut -d '=' -f2)
	else
		logMessagePatch "FATAL" "fsafnd_image_versions.props not present"
        exit 0
	fi

    if [[ -f "dfcs-deployment-job.yaml" ]]; then
        logMessagePatch "INFO" "Replacing placeholders in dfcs-deployment-job.yaml file..."
		patch::replace_placeholders "dfcs-deployment-job.yaml" "false"
		
	    sed -i \
			-e "s|__TAG__|$image_version|g" \
			dfcs-deployment-job.yaml
    else
        logMessagePatch "FATAL" "dfcs-deployment-job.yaml file not present"
        exit 0
    fi

    # job name
    component="dfcs-deployment-job"

    cat dfcs-deployment-job.yaml

    echo -e "\n Checking if $component kubernetes job is already present"
    if kubectl get jobs -n ${SUB_NAMESPACE} | grep -qw ${component}; then
        logMessagePatch "INFO" "Job ${component} exists."
        logMessagePatch "INFO" "deleting $component kubernetes job"
	    kubectl -n ${SUB_NAMESPACE} delete jobs ${component}
	    logMessagePatch "INFO" "deleted $component kubernetes job successfully"
    else
        logMessagePatch "INFO" "Job ${component} does not exist."
    fi

	logMessagePatch "INFO" "Executing $component kubernetes job"
	echo "Calling method : k8s_create_deployment_job"
    k8s_create_deployment_job "${SUB_NAMESPACE}" "${component}" dfcs-deployment-job.yaml
    logMessagePatch "INFO" "kubernetes $component job has been executed successfully"
}

#Execution Starts 
patch::initialize
#deployment_upgrading_flag_set
dfcs_deployment_job_exec
deployment_exec_check
