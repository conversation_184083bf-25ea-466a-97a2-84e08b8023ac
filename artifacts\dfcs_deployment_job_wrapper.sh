#!/bin/bash
set -e

download_wallet(){   
	
	NAMESPACE=${1^^} 
	WALLET_DL_PATH="$WALLET_PATH/wallet-$NAMESPACE"	
	download_loc="/opt/wallet/$NAMESPACE"     
	SAT=$(cat /opt/sat/token)
	logMessagePatch "INFO" "Downloading wallet from - $WALLET_DL_PATH"       
	mkdir -p ${download_loc}
	rm -rf $download_loc/*       
	curl -s --insecure -X GET -H "Authorization:Bearer ${SAT}"  $WALLET_DL_PATH | jq -r '.walletdata' | base64 -d --wrap=0 > wallet.zip
	logMessagePatch "INFO" "wallet downloaded"
	unzip -q wallet.zip -d ${download_loc}
	logMessagePatch "INFO" "unzipping wallet"
	rm -rf wallet.zip
	export TNS_ADMIN=${download_loc}
	export TNS_URL=`cat ${TNS_ADMIN}/tnsnames.ora | grep -m 1 . |  cut -f2- -d "=" | xargs|tr -d '\n'|tr -d '\r'`

}

update_status(){
    logMessagePatch "INFO" "Updating UPGRADED flag"
    
    NAMESPACE=${1^^} 
    STATUS=${2^^}
	APP_VER=${3^^}

    if [[ $NAMESPACE == "FSGBU-FSAFND" ]]; then
		logMessagePatch "INFO" "Downloading wallet"
        download_wallet "FSGBU-DFCS"

        output=$(sqlplus -s "/@DFCSMETA" << EOF
        set echo on heading off feedback off SERVEROUTPUT ON trimout on tab off;
        UPDATE DFCS_DOMAIN_DETAILS SET UPGRADED = '$STATUS', UPGRADE_VERSION = '$APP_VER' WHERE DOMAIN_ID = 'Banking';
        COMMIT;
        exit;
EOF
        )

        if [[ $? -ne 0 ]]; then
            logMessagePatch "ERROR" "Failed to update UPGRADED flag."
            return 1
        fi

        logMessagePatch "INFO" "Status $STATUS updated in DFCS_DOMAIN_DETAILS table"
        echo "$output"
    fi  
} 

deploymentCompletedStatusCheck(){
    logMessagePatch "INFO" "Checking status of all deployment steps"
    download_wallet "$MSP_PROJ_NAME"

    DS_DETAILS=$(sqlplus -s "/@CATMETA" << EOF
    set echo on heading off feedback off SERVEROUTPUT ON trimout on tab off;
    set colsep '|';

    SELECT APP_VERSION || '|' || PROC_NAME || '|' || PROC_STATUS 
    FROM (
        SELECT * FROM DFCS_CONFIGURATION_STATUS
        WHERE APP_VERSION = (SELECT PARAMCODE FROM DP_DEP_APP_VER_MASTER WHERE PARAMNAME = 'DPDeployment')
        AND PROC_STATUS NOT IN ('C')
        ORDER BY RECORD_ID
    ) WHERE ROWNUM = 1;
    exit;
EOF
    )

    if [[ $? -ne 0 ]]; then
        logMessagePatch "ERROR" "SQL query execution failed"
        return 1
    fi

    DS_DETAILS=$(echo "$DS_DETAILS" | tr -d '\n\t' | sed 's/^ *//;s/ *$//')

    if [[ -n "$DS_DETAILS" ]]; then
        logMessagePatch "INFO" "Deployment Status Details: $DS_DETAILS"
        
        PROC_NAME=$(echo "$DS_DETAILS" | cut -d '|' -f2)
        PROC_STATUS=$(echo "$DS_DETAILS" | cut -d '|' -f3)

        logMessagePatch "ERROR" "Deployment Steps are not completed"
        logMessagePatch "ERROR" "Deployment stopped at $PROC_NAME. Status: $PROC_STATUS"
        exit 1
    else
        logMessagePatch "INFO" "All Steps are completed"

        APP_VER_DTLS=$(sqlplus -s "/@CATMETA" << EOF
        set echo on heading off feedback off SERVEROUTPUT ON trimout on tab off;
        set colsep '|';

        SELECT PARAMCODE FROM DP_DEP_APP_VER_MASTER WHERE PARAMNAME = 'DPDeployment';
        exit;
EOF
        )

        APP_VER=$(echo "$APP_VER_DTLS" | tr -d '\n\t' | sed 's/^ *//;s/ *$//')

        if [[ -n "$APP_VER_DTLS" ]]; then
            update_status "$MSP_PROJ_NAME" "Y" "$APP_VER"
        fi
    fi
}

checkAndPerformTask(){
	echo "Task Name : $TASK_NAME"
	if [[ $TASK_NAME == "SET_UPGRADED_FLAG" ]]; then
		update_status "$MSP_PROJ_NAME" "O" "$APP_VER"	
	elif [[ $TASK_NAME == "CHECK_DEPLOYMENT_STEPS" ]]; then
		logMessagePatch "INFO" "Checking deployment steps status"
		deploymentCompletedStatusCheck
	fi
}

initialize(){
  basedir=$(pwd)
  artstore -s -e k8s -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY} file pull ${ARTSTORE_PATH}/common_functions.sh ./ -r -f 
  chmod +x $basedir/common_functions.sh
  source $basedir/common_functions.sh   
}

initialize
checkAndPerformTask

