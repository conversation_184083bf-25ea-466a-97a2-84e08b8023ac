#!/bin/bash

echo "DFCS metaconf Execution stated"
chmod +x ./artstore
download_loc=/opt/wallet

download_wallet(){    
    cluster=${2^^}
    download_loc=${download_loc}/$cluster     
    SAT=$(cat /opt/sat/token)
    echo "Downloading wallet from - $1"       
    mkdir -p ${download_loc}
    rm -rf $download_loc/*       
    curl -s --insecure -X GET -H "Authorization:Bearer ${SAT}"  $1 | jq -r '.walletdata' | base64 -d --wrap=0 > wallet.zip
    unzip -q wallet.zip -d ${download_loc}
    rm -rf wallet.zip
    export TNS_ADMIN=${download_loc}
    export TNS_URL=`cat ${TNS_ADMIN}/tnsnames.ora | grep -m 1 . |  cut -f2- -d "=" | xargs|tr -d '\n'|tr -d '\r'`
}

initialize(){
  basedir=$(pwd)
  artstore -s -e k8s -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY} file pull ${ARTSTORE_PATH}/common_functions.sh ./ -r -f 
  artstore -s -e k8s -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY} file pull -rf  ${DEP_ARTIFACTS_PATH}/scripts scripts/   >/dev/null
  chmod +x $basedir/common_functions.sh
  source $basedir/common_functions.sh   
}

#Start Execution
initialize
if [[ $PATCH_STAGE == "dbScripts" ]];then
cd scripts
if [[ ${MSP_PROJ_NAME} == "fsgbu-fsafnd" ]];then
   echo "Downloading wallet for fsafnd.."
   download_wallet "${WALLET_PATH}/wallet-FSGBU-FSAFND" "FSGBU-FSAFND"
if [[ -f "grants_fsafnd.sql" ]];then
if [[ $(fileContains "/" "grants_fsafnd.sql") == "true" ]];then
	echo "Executing grants_fsafnd.sql ..."
	ADMIN_USER="ADMIN"
	SAT=$(cat /opt/sat/token)
	export ADMIN_IDENTIFIER=$(curl -s --insecure -X GET -H "Authorization:Bearer ${SAT}"  "${WALLET_PATH}"/ADMIN | jq -r '.password')
	export ADMIN_IDENTIFIER_ORG="${ADMIN_IDENTIFIER}"
	ADMIN_ALIAS=${DB_ALIAS}
	echo 'Connect="${ADMIN_USER}/\"${ADMIN_IDENTIFIER}\""@${ADMIN_ALIAS}'
sqlplus -s "${ADMIN_USER}/\"${ADMIN_IDENTIFIER}\""@${ADMIN_ALIAS} << EOF
set echo on heading off  feedback off SERVEROUTPUT ON trimout on tab off;
WHENEVER SQLERROR continue;
prompt - Executing grants_fsafnd.sql;
@grants_fsafnd.sql
exit;
EOF
fi
fi
fi
if [[ ${MSP_PROJ_NAME} == "fsgbu-dfcs" ]];then
	echo "Downloading wallet for dfcs.."
	download_wallet "${WALLET_PATH}/wallet-FSGBU-DFCS" "FSGBU-DFCS"
if [[ -f "grants_dfcs.sql" ]];then
if [[ $(fileContains "/" "grants_dfcs.sql") == "true" ]];then

	echo "Executing grants_dfcs.sql ..."
	ADMIN_USER="ADMIN"
	SAT=$(cat /opt/sat/token)
	export ADMIN_IDENTIFIER=$(curl -s --insecure -X GET -H "Authorization:Bearer ${SAT}"  "${WALLET_PATH}"/ADMIN | jq -r '.password')
	export ADMIN_IDENTIFIER_ORG="${ADMIN_IDENTIFIER}"
	ADMIN_ALIAS=${DB_ALIAS}

sqlplus -s "${ADMIN_USER}/\"${ADMIN_IDENTIFIER}\""@${ADMIN_ALIAS} << EOF
set echo on heading off  feedback off SERVEROUTPUT ON trimout on tab off;
WHENEVER SQLERROR continue;
prompt - Executing grants_dfcs.sql;
@grants_dfcs.sql
exit;
EOF
fi
fi
fi
fi

echo "execution completed !!!"
