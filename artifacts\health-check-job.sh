#!/bin/bash
source common_functions.sh

initialize() {
	
	CONTAINER=$(echo "$JOB_NAME" | sed -E 's/^health-check-//; s/-exec$//')
	logMessagePatch "LOG" "Start healthcheck for container - $CONTAINER"
	TIMEOUT_SECONDS=3600
	RETRY_INTERVAL=30
	START_TIME=$(date +%s)
	
	srvc_config='{"common-dm": [{"catalog" : "data-foundation"}, {"dsa-ui" : "dsa-ui"}, {"glrecon" : "glrecon"}, {"rse" : "rse"}, {"ina" : "ina"}], "dm-engine" : [{"dcaf" : "dcaf"}, {"dsa-core-engine" : "dsa-core-engine"}], "dfcscore" : [{"dfcscore" : "dfcscore"}]}'
	
	output=$(echo "$srvc_config" | jq -r --arg key "$CONTAINER" 'to_entries[] | select(.value | any(has($key))) | "\(.key) \(.value[] | select(has($key))[$key])"')
	# Split the output into two variables
	SERVICE_NAME=$(echo "$output" | awk '{print $1}')
	service_host=$(echo "$output" | awk '{print $2}')
	API_URL="http://${service_host}:8080/health"
	logMessagePatch "LOG" "Start healthcheck for SERVICE_NAME - $SERVICE_NAME"
	logMessagePatch "LOG" "Start healthcheck for service_host - $service_host"
	logMessagePatch "LOG" "Start healthcheck for API_URL - $API_URL"

}

# Function: Find a running pod for the specified service
find_running_pod() {
    
	local label_selector="app.kubernetes.io/name=$SERVICE_NAME"
    echo >&2 $(logMessagePatch "LOG" "Checking for a running pod..")
    while true; do
        local running_pod
        running_pod=$(kubectl -n "$MSP_PROJ_NAME--$TENANT_ID" get pods -l "$label_selector" -o=json | \
        jq -r '.items[] | select(.status.phase == "Running" and .metadata.deletionTimestamp == null and 
        (.status.containerStatuses | all(.state.waiting.reason != "CrashLoopBackOff" and .state.waiting.reason != "CreateContainerConfigError"))) | .metadata.name' | head -n 1)
       if [[ -n "$running_pod" ]]; then
            echo >&2 $(logMessagePatch "LOG" "Found a running pod: $running_pod") 
            echo "$running_pod"
            return 0
        fi
        # Check timeout
        local current_time elapsed_time
        current_time=$(date +%s)
        elapsed_time=$((current_time - START_TIME))
        if ((elapsed_time >= TIMEOUT_SECONDS)); then
            echo >&2 $(logMessagePatch "ERROR" "Timeout reached while waiting for a running pod.")
            exit 1
        fi

        echo >&2 $(logMessagePatch "LOG" "No running pod found. Retrying in $RETRY_INTERVAL seconds...")
        sleep $RETRY_INTERVAL
    done

}

# Function: Check if the container is running (with retry logic)
is_container_running() {
    
	pod_name="$1"
    while true; do
        local pod_valid
        pod_valid=$(kubectl -n "$MSP_PROJ_NAME--$TENANT_ID" get pod "$pod_name" -o=json | \
        jq -r '. | select(.status.phase == "Running" and .metadata.deletionTimestamp == null and 
        (.status.containerStatuses | all(.state.waiting.reason != "CrashLoopBackOff" and .state.waiting.reason != "CreateContainerConfigError"))) | .metadata.name' 2>/dev/null || echo "NOT_VALID")
        
        if [[ -z "$pod_valid" || "$pod_valid" == "NOT_VALID" ]]; then
            echo >&2 $(logMessagePatch "LOG" "Pod $1 is not in Running state. Checking for a new running pod...")
            pod_name=$(find_running_pod) # Search for a new valid pod
            continue # Restart the loop with the new pod
        fi

        # Check if the container is running
        local container_running
        container_running=$(kubectl -n "$MSP_PROJ_NAME--$TENANT_ID" get pod "$pod_valid" \
            -o=jsonpath="{range .status.containerStatuses[?(@.name=='$CONTAINER')]}{.state.running}{end}" 2>/dev/null || echo "")

        if [[ -n "$container_running" ]]; then
            echo "$pod_valid"
            return 0
        fi

        # Check timeout
        local current_time elapsed_time
        current_time=$(date +%s)
        elapsed_time=$((current_time - START_TIME))
        if ((elapsed_time >= TIMEOUT_SECONDS)); then
            echo >&2 $(logMessagePatch "ERROR" "Timeout reached while waiting for container - $CONTAINER to be running.")
            exit 1
        fi

        echo >&2 $(logMessagePatch "LOG" "Container - $CONTAINER is not yet running in pod - $pod_valid. Retrying in $RETRY_INTERVAL seconds...")
        sleep "$RETRY_INTERVAL"
    done

}

# Function: Poll the health API until all checkss and overall status are "UP"
poll_health_api() {

	logMessagePatch "LOG" "Performing pod health check..."
	while true; do
		container_name=$(is_container_running "$pod_name")
		local health_response success_checks failed_checks consolidated_msg
		set +e
		health_response=$(make_api_request "$API_URL" "GET" "" "" "" "" 3 10)
		set -e
		# Extract overall status
		local overall_status
		overall_status=$(echo "$health_response" | jq -r '.status // "Unknown"')
		if [[ ! -z $overall_status && overall_status != "Unknown" ]];then        
			if [[ "${overall_status^^}" == "UP" ]];then
				logMessagePatch "INFO" "Overall status: $overall_status"
			else
				logMessagePatch "WARN" "Overall status: $overall_status"
			fi
		
			# Process all checks (success and failed)
			success_checks=$(echo "$health_response" | jq -c '.checks[]')
			consolidated_msg=""
			failed_checks=""
			if [[ -n "$success_checks" ]]; then
				while read -r check; do
					check_name=$(echo "$check" | jq -r '.name // empty')
					check_status=$(echo "$check" | jq -r '.status // empty')
					if [[ "$check_status" == "UP" ]]; then
						consolidated_msg+="$check_name -- UP : "
					else
						# Include in consolidated message
						consolidated_msg+="$check_name -- $check_status : "
						# Add to failed checks
						check_data=$(echo "$check" | jq -c '.data // empty')
						if [[ -n "$check_data" ]]; then
							failed_checks+="$check_name -- $check_status -- Data: $check_data\n"
						else
							failed_checks+="$check_name -- $check_status -- No additional data\n"
						fi
					fi
				done < <(echo "$success_checks" | jq -c '.')
	
				# Remove trailing separator
				if [[ "$consolidated_msg" == *" : " ]]; then
					consolidated_msg=${consolidated_msg::-3}
				fi
			fi
	
			# Log consolidated checks based on the presence of failed checks
			if [[ -n "$failed_checks" ]]; then
				logMessagePatch "WARN" "$consolidated_msg"
			else
				logMessagePatch "INFO" "$consolidated_msg"
			fi
	
			# Log failed checks, if any
			if [[ -n "$failed_checks" ]]; then
				logMessagePatch "WARN" "Failed checks detected:"
				while IFS= read -r line; do
					logMessagePatch "LOG" "$line"
				done <<< "$failed_checks"
			fi
		
			# Exit if all checks are successful
			if [[ "$overall_status" == "UP" && -z "$failed_checks" ]]; then
				logMessagePatch "INFO" "All checks for $CONTAINER completed successfully. Service is healthy."
				break
			fi
		fi
		# Timeout check
		local current_time elapsed_time
		current_time=$(date +%s)
		elapsed_time=$((current_time - START_TIME))
		if ((elapsed_time >= TIMEOUT_SECONDS)); then
			logMessagePatch "ERROR" "All checks are not successful. Setting health check status to FAILED. The pipeline will exit"
			exit 1
		fi
	#
		logMessagePatch "LOG" "Retrying health check in $RETRY_INTERVAL seconds..."
		sleep "$RETRY_INTERVAL"
	done

}


# Script Execution starts
initialize
pod_name=$(find_running_pod)
poll_health_api 
