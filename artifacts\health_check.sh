#!/bin/bash
set -e
CONTAINER="$PATCH_ACTION"
replaceParams(){

	sed -i \
			-e "s|{{PREFIX}}|${PREFIX}|g" \
			-e "s|{{REGISTRY}}|${REGISTRY}|g" \
			-e "s|__artstore_path__|${ARTSTORE_PATH}|g" \
			-e "s|__deployments_artifacts_path__|${deployments_artifacts_path}|g" \
			-e "s|__OST_ENVIRONMENT__|${OST_ENVIRONMENT}|g" \
			-e "s|__PATCH_STAGE__|${PATCH_STAGE}|g" \
			-e "s|__PATCH_ID__|${PATCH_ID}|g" \
			-e "s|__REGION__|${oci_region}|g" \
			-e "s|__REGION_CODE__|${REGION_CODE}|g" \
			-e "s|__TENANCY__|${TENANCY}|g" \
			-e "s|__WALLET_PATH__|${BASE_SECRET_URLS}${TENANT_ID}|g" \
			-e "s|__TENANT_ID__|${TENANT_ID}|g" \
			-e "s|__BASE_SECRET_URLS__|${BASE_SECRET_URLS}|g" \
			-e "s|__ADW__|true|g" \
			-e "s|__job_exec_name__-job.sh|health-check-job.sh|g" \
			-e "s|__job_exec_name__|health-check-$CONTAINER|g" \
			-e "s|__CI_PIPELINE_ID__|${CI_PIPELINE_ID}|g" \
			-e "s|__API_POLL_COUNT__|${API_POLL_COUNT}|g" \
			-e "s|__PATCH_VERSION__|${PATCH_VERSION}|g" \
			-e "s|__API_POLL_INTERVAL__|${API_POLL_INTERVAL}|g" \
			-e "s|__MSP_PROJ_NAME__|${MSP_PROJ_NAME}|g" \
			./patch_job_handler.yaml.tpl
	
	mv patch_job_handler.yaml.tpl health-check-$CONTAINER-job.yaml
	cat health-check-$CONTAINER-job.yaml

}
deploy_health_check(){
	echo "In deploy_health_check method"
	chmod +x health-check-job.sh
	logprefix=`logMessagePatch "INFO" ""`
	replaceParams
	echo "Sub Namespace:$SUB_NAMESPACE"
	kubectl -n  "${SUB_NAMESPACE}" delete job health-check-$CONTAINER-exec --ignore-not-found --grace-period=0 --force 2>/dev/null
	echo "Job health-check-$CONTAINER-exec deleted succesfully"
	kubectl -n "${SUB_NAMESPACE}" delete configmap health-check-$CONTAINER-job --ignore-not-found
	echo "Configmap health-check-$CONTAINER-job deleted succesfully"
	kubectl -n "${SUB_NAMESPACE}" create configmap health-check-$CONTAINER-job --from-file=health-check-job.sh
	echo "Configmap health-check-$CONTAINER-job created succesfully"
	k8s_create_job "${SUB_NAMESPACE}" "health-check-$CONTAINER-exec" "health-check-$CONTAINER-job.yaml"
	echo "Job created..."
	result=$(kubectl -n ${SUB_NAMESPACE}  get pods --selector=job-name="health-check-$CONTAINER-exec" --output=jsonpath='{.items[0].status.phase}')

	if [[ "${result^^}" == "FAILED" ]]; then
		logMessagePatch "INFO" "health-check-$CONTAINER-job has been failed."
		exit -1
	fi

}

#Execution Starts
deploy_health_check
logMessagePatch "INFO" "health-check-$CONTAINER-job Successful."
