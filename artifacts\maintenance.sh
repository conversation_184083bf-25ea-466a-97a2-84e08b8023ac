#!/bin/bash
set -e
rm -rf maintenance_dir
mkdir -p maintenance_dir
MAINTAINCE_DIR=./maintenance_dir

deploy::update_cp(){
	
	local flag="$1"
	rm -rf ./$MAINTAINCE_DIR/configuration.properties
	kubectl -n ${SUB_NAMESPACE} get secret > secret.list
	if ! grep -q "configuration.properties" "secret.list"; then
		return;
	fi
	kubectl -n ${SUB_NAMESPACE} get secret configuration.properties -o jsonpath='{.data.configuration\.properties}' | base64 -d > ././$MAINTAINCE_DIR/configuration.properties
	sed -i '/IS_MAINTENANCE_ENABLED/d' ./$MAINTAINCE_DIR/configuration.properties
	echo -e -n "\nIS_MAINTENANCE_ENABLED=${flag}" >> ./$MAINTAINCE_DIR/configuration.properties
	sed -i '/^$/d' ./$MAINTAINCE_DIR/configuration.properties
	kubectl -n ${SUB_NAMESPACE} create secret generic configuration.properties --from-file=./$MAINTAINCE_DIR/configuration.properties -o yaml --dry-run | kubectl  apply -f -
	
}

deploy::mode(){
	
	local op="$1"
	cp wtss.svc.yaml.tpl ./$MAINTAINCE_DIR/wtss.svc.yaml
	cp main.svc.yaml.tpl ./$MAINTAINCE_DIR/main.svc.yaml
	sed -i \
		-e "s|__ENVIRONMENT_ID__|${TENANT_ID}|g" \
		-e "s|__COMMON_NAMESPACE__|${COMMON_NAMESPACE,,}|g" \
		./$MAINTAINCE_DIR/*.yaml

	case $op in
		  "TRUE")
		    logMessagePatch "INFO" "Enabling Maintenance Mode for tenant :  ${TENANT_ID}"
		    deploy::update_cp "true" "${SUB_NAMESPACE}"  &>/dev/null
		    sleep 1m
		    kubectl -n $SUB_NAMESPACE apply -f ./$MAINTAINCE_DIR/main.svc.yaml
		    ;;

		  "FALSE")
		    logMessagePatch "INFO" "Exiting Maintenance Mode for tenant :  ${TENANT_ID}"
		    deploy::update_cp "false" "${SUB_NAMESPACE}" &>/dev/null
		    sleep 1m
		    kubectl -n $SUB_NAMESPACE apply -f ./$MAINTAINCE_DIR/wtss.svc.yaml
		    ;;
		  *)
   			logMessagePatch "FATAL" "Invalid mode operation ${op}"
   			exit 1
   		;;
	esac
	
	logMessagePatch "INFO" "Maintenance Mode ha been successfully changed to - ${op}"
	
}

deploy::mode "${IS_MAINTENANCE_ENABLED^^}"
