---
apiVersion: v1
kind: ConfigMap
metadata:
  name: configure-dv-status
  annotations:
    version: v1.0
data:
  configure-dv-status.sh: |-
    #!/bin/bash
    set +e
    export CMPT_OCID=$ADB_INSTANCE_OCID
    /home/<USER>/generate.sh
    CONFIG_FILE="/home/<USER>/config"

    oci --config-file="${CONFIG_FILE}" db autonomous-database get --autonomous-database-id "${ADB_INSTANCE_OCID}" > response.json
    state=$(jq -r '.data."lifecycle-state"' response.json)

    echo "Current State : ${state}"
    echo "Input DV Action : ${DV_ACTION}"