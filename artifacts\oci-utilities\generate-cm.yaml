apiVersion: v1
kind: ConfigMap
metadata:
  name: generate-cm
  annotations:
    version: v1.0
data:
  generate.sh: |-
    #!/bin/bash
    set +e
    if [ -z "${CMPT_OCID}" ]; then
      echo "CMPT_OCID env variable is not set"
      exit 1
    fi
    
    export OCI_CONFIG_DIR="/home/<USER>" || exit 1
    export OCI_CLI_CONFIG_FILE=${OCI_CONFIG_DIR}/config
    export OCI_CLI_SUPPRESS_FILE_PERMISSIONS_WARNING=True
    oci_config_file=${OCI_CLI_CONFIG_FILE}
    oci_fp_file=${OCI_CONFIG_DIR}/ocise.fp
    oci_env_file=${OCI_CONFIG_DIR}/ocise.env
    oci_private_key_file=${OCI_CONFIG_DIR}/private_key.pem
    
    function generate_oci_config() {
      region_code=$(cat ~/.secrets/ocise001/region-code | tr [:upper:] [:lower:])
    
    cat > ${oci_config_file} << EOF
    [DEFAULT]
    tenancy=$(cat ~/.secrets/ocise001/tenant)
    region=${OCI_REGION}
    user=$(cat ~/.secrets/ocise001/user)
    fingerprint=$(cat ~/.secrets/ocise001/fingerprint)
    key_file=${OCI_CONFIG_DIR}/private_key.pem
    EOF
    
      ## Generate the private key file
    cat > ${oci_private_key_file} << EOF
    $(cat ~/.secrets/ocise001/key)
    EOF
    
    }
    generate_oci_config
