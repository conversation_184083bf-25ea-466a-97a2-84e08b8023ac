#!/usr/bin/env python
# Parses TAS JSON payload and prints information to stdout

import os
import sys
import json
import string
import random
import datetime
import xml.etree.ElementTree as ET


jsonFilePath = sys.argv[1]
PATCH_HOME = sys.argv[2]
OST_ENVIRONMENT = sys.argv[3]
OST_CLASS = sys.argv[4]
TENANT_ID = sys.argv[5]


FSAFND_AAI_DEPLOYMENT_NAMES = []
FSAFND_AAI_SVC_NAMES = []
FSAFND_APP_DEPLOYMENT_NAMES = []
FSAFND_APP_SVC_NAMES = []
DFCS_APP_DEPLOYMENT_NAMES = []
DFCS_APP_SVC_NAMES = []
PATCH_IDS = []

if os.stat(jsonFilePath).st_size == 0:
    sys.stderr.write("File payload.json is Empty")
    exit(-1)

def prop_append(fileObj, propKey, propVal, isArrayVal):
    if propVal is not None:
        if isArrayVal == False:
            fileObj.write(propKey + "=" + propVal + '\n')
        else:
            fileObj.write(propKey + "=" + ','.join(propVal) + '\n')

def sort(arr, sortPropKey, isReverse):
    arr.sort(key=lambda json: json[sortPropKey], reverse=isReverse)

def push(payload, sortIndex, extractKey, resArr):
    objArr = payload
    sort(objArr, sortIndex, False)
    for i in objArr:
        if extractKey in i:
            resArr.append(i[extractKey])


def safe_push_deployments(payload, key, index_key, value_key, result_array, warning_message):
    if 'services-deployments' in payload and key in payload['services-deployments']:
        push(payload['services-deployments'][key], index_key, value_key, result_array)
try:
    with open(jsonFilePath, 'r') as payload_file:
        payload = json.load(payload_file)
except Exception as ex:
    print(ex)
    print("Unable to convert PAYLOAD to JSON:" + jsonFilePath)
    print("TAS STATUS CODE: Fail")
    exit(-1)

# Open the environment file for writing
env_file = open(PATCH_HOME + '/patch_pipeline_services.env', 'w')
prop_append(env_file, 'TENANT_ID', TENANT_ID, False)
prop_append(env_file, 'OST_CLASS', OST_CLASS, False)
prop_append(env_file, 'OST_ENVIRONMENT', OST_ENVIRONMENT, False)
prop_append(env_file, 'EXECUTE_SCRIPTS', payload.get('EXECUTE_SCRIPTS'), False)
prop_append(env_file, 'SKIP_SC', payload.get('SKIP_SC'), False)
prop_append(env_file, 'UPGRADE_DIS', payload.get('UPGRADE_DIS'), False)
prop_append(env_file, 'UPDATE_WTSS', payload.get('UPDATE_WTSS'), False)
prop_append(env_file, 'DEPLOY_FSS_SERVICES', payload.get('DEPLOY_FSS_SERVICES'), False)
prop_append(env_file, 'ON_OFF_DV_CONFIG', payload.get('ON_OFF_DV_CONFIG'), False)
prop_append(env_file, 'DEPLOY_AAI', payload.get('DEPLOY_AAI'), False)
prop_append(env_file, 'USER_GROUPS_CREATION', payload.get('USER_GROUPS_CREATION'), False)
prop_append(env_file, 'INITIATE_DEPLOYMENT', payload.get('INITIATE_DEPLOYMENT'), False)
prop_append(env_file, 'DFCS_CONTENT', payload.get('DFCS_CONTENT'), False)


# Safely push deployment data
safe_push_deployments(payload, 'fsafndAaiServices', 'index', 'deployFileName', FSAFND_AAI_DEPLOYMENT_NAMES, "WARNING: 'fsafndAaiServices' key is missing in the payload.")
safe_push_deployments(payload, 'fsafndAaiServices', 'index', 'svcFileName', FSAFND_AAI_SVC_NAMES, "WARNING: 'fsafndAaiServices' key is missing in the payload.")

safe_push_deployments(payload, 'fsafndAppServices', 'index', 'deployFileName', FSAFND_APP_DEPLOYMENT_NAMES, "WARNING: 'fsafndAppServices' key is missing in the payload.")
safe_push_deployments(payload, 'fsafndAppServices', 'index', 'svcFileName', FSAFND_APP_SVC_NAMES, "WARNING: 'fsafndAppServices' key is missing in the payload.")

safe_push_deployments(payload, 'dfcsAppServices', 'index', 'deployFileName', DFCS_APP_DEPLOYMENT_NAMES, "WARNING: 'dfcsAppServices' key is missing in the payload.")
safe_push_deployments(payload, 'dfcsAppServices', 'index', 'svcFileName', DFCS_APP_SVC_NAMES, "WARNING: 'dfcsAppServices' key is missing in the payload.")
prop_append(env_file, 'FSAFND_AAI_DEPLOYMENT_NAMES', FSAFND_AAI_DEPLOYMENT_NAMES, True)
prop_append(env_file, 'FSAFND_APP_DEPLOYMENT_NAMES', FSAFND_APP_DEPLOYMENT_NAMES, True)
prop_append(env_file, 'FSAFND_APP_SVC_NAMES', FSAFND_APP_SVC_NAMES, True)
prop_append(env_file, 'DFCS_APP_DEPLOYMENT_NAMES', DFCS_APP_DEPLOYMENT_NAMES, True)
prop_append(env_file, 'DFCS_APP_SVC_NAMES', DFCS_APP_SVC_NAMES, True)

print("ENV File Generated")
env_file.close()
