apiVersion: batch/v1
kind: Job
metadata:
  name: __job_exec_name__-exec
spec:
  backoffLimit: 1
  ttlSecondsAfterFinished: 900
  template:
    metadata:
      annotations:
        version: v1.0
      labels:
        component: __job_exec_name__-exec
    spec:
      containers:
        - image: {{REGISTRY}}/{{PREFIX}}-__MSP_PROJ_NAME__/dfcs-upgrade-pipeline:__PATCH_VERSION__
          name: __job_exec_name__-exec
          imagePullPolicy: Always
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: JOB_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['job-name']
            - name: TENANCY
              value: "__TENANCY__"
            - name: REGION_CODE
              value: "__REGION_CODE__"
            - name: DEP_ARTIFACTS_PATH
              value: "__deployments_artifacts_path__"
            - name: MSP_PROJ_NAME
              value: "__MSP_PROJ_NAME__"
            - name: PATCH_STAGE
              value: "__PATCH_STAGE__"
            - name: ARTSTORE_PATH
              value: "__artstore_path__"
            - name: WALLET_PATH
              value: "__WALLET_PATH__"
            - name: TENANT_ID
              value: "__TENANT_ID__"
            - name: CI_PIPELINE_ID
              value: "__CI_PIPELINE_ID__"
            - name: api_poll_count
              value: "__API_POLL_COUNT__"
            - name: api_poll_interval
              value: "__API_POLL_INTERVAL__"
            - name: OST_ENVIRONMENT
              value: "__OST_ENVIRONMENT__"
            - name: PATCH_VERSION
              value: "__PATCH_VERSION__"
            - name: DB_ALIAS
              valueFrom:
                secretKeyRef:
                  name: dbaas-details
                  key: DB_ALIAS 
          command:
            - 'sh'
            - '-c'
            - '/home/<USER>/__job_exec_name__-job.sh'
          volumeMounts:            
            - name: exec-volume
              mountPath: /home/<USER>/__job_exec_name__-job.sh
              subPath: __job_exec_name__-job.sh
            - name: wallet-vol
              mountPath: /opt/wallet
            - name: oci-vol
              mountPath: /os_parameters
            - name: sat-vol
              mountPath: /opt/sat/token
              subPath: sat
          securityContext:
            runAsNonRoot: true
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            seccompProfile:
              type: RuntimeDefault
      restartPolicy: Never
      serviceAccountName: namespace-admin
      securityContext:
        runAsUser: 1000
      volumes:
        - name: wallet-vol
          emptyDir: {}
        - name: exec-volume
          configMap:
            defaultMode: 0777
            name: __job_exec_name__-job
        - name: oci-vol
          secret:
            secretName: reserved-object-store-entity
        - name: sat-vol
          secret:
            secretName: reserved-service-access-token
