#!/bin/bash
set -e

replaceParams(){

sed -i \
        -e "s|{{PREFIX}}|${PREFIX}|g" \
        -e "s|{{REGISTRY}}|${REGISTRY}|g" \
        -e "s|__artstore_path__|${ARTSTORE_PATH}|g" \
        -e "s|__deployments_artifacts_path__|${deployments_artifacts_path}|g" \
        -e "s|__PATCH_STAGE__|${PATCH_STAGE}|g" \
        -e "s|__PATCH_ID__|${PATCH_ID}|g" \
        -e "s|__REGION__|${oci_region}|g" \
        -e "s|__REGION_CODE__|${REGION_CODE}|g" \
        -e "s|__TENANCY__|${TENANCY}|g" \
        -e "s|__WALLET_PATH__|${BASE_SECRET_URLS}${TENANT_ID}|g" \
        -e "s|__TENANT_ID__|${TENANT_ID}|g" \
        -e "s|__BASE_SECRET_URLS__|${BASE_SECRET_URLS}|g" \
        -e "s|__ADW__|true|g" \
        -e "s|__MSP_PROJ_NAME__|${MSP_PROJ_NAME}|g" \
        ./precheck-job-wrapper.yaml
		
		cat ./precheck-job-wrapper.yaml

}

precheck(){

	chmod +x precheck_job_wrapper.sh
	logprefix=`logMessagePatch "INFO" ""`
	replaceParams
	echo "sub_namespace----- : ${SUB_NAMESPACE}"
	kubectl -n  "${SUB_NAMESPACE}" delete job precheck-job-exec --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" delete configmap precheck-job-wrapper --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" create configmap precheck-job-wrapper --from-file=precheck_job_wrapper.sh
	k8s_create_job "${SUB_NAMESPACE}" "precheck-job-exec" "precheck-job-wrapper.yaml"
	result=$(kubectl -n ${SUB_NAMESPACE}  get pods --selector=job-name="precheck-job-exec" --output=jsonpath='{.items[0].status.phase}')
	if [[ "${result^^}" == "FAILED" ]]; then
		exit -1
	fi

}

#execution starts
patch::initialize
precheck


