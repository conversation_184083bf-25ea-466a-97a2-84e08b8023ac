#!/bin/bash
set -e

download_wallet(){   
	
	NAMESPACE=${1^^} 
	WALLET_DL_PATH="$WALLET_PATH/wallet-$NAMESPACE"	
	download_loc="/opt/wallet/$NAMESPACE"     
	SAT=$(cat /opt/sat/token)
	logMessagePatch "INFO" "Downloading wallet from - $WALLET_DL_PATH"       
	mkdir -p ${download_loc}
	rm -rf $download_loc/*       
	curl -s --insecure -X GET -H "Authorization:Bearer ${SAT}"  $WALLET_DL_PATH | jq -r '.walletdata' | base64 -d --wrap=0 > wallet.zip
	logMessagePatch "INFO" "wallet downloaded"
	unzip -q wallet.zip -d ${download_loc}
	logMessagePatch "INFO" "unzipping wallet"
	rm -rf wallet.zip
	export TNS_ADMIN=${download_loc}
	export TNS_URL=`cat ${TNS_ADMIN}/tnsnames.ora | grep -m 1 . |  cut -f2- -d "=" | xargs|tr -d '\n'|tr -d '\r'`

}

crPrecheck(){
logMessagePatch "INFO" "Starting precheck for Failed/Pending CR.."
download_wallet "$MSP_PROJ_NAME"
logMessagePatch "INFO" "Validating Custom CRs"
CR_DETAILS=$(sqlplus -s "/@CATMETA" << EOF
set echo on heading off feedback off SERVEROUTPUT ON trimout on tab off;
SET COLSEP '|'
SELECT LISTAGG(TRIM(cro.n_cr_id) || '|' || cro.v_status, ':') WITHIN GROUP (ORDER BY cro.n_cr_id) AS result
FROM dc_cr_object cro
INNER JOIN (
SELECT DISTINCT crm.v_cr_release_id cr_id
FROM dc_run_cr_map crm
INNER JOIN dc_custom_deploy_wf cdw ON crm.n_run_id = cdw.n_run_id
AND cdw.v_status = 'FAILED'
INNER JOIN (
SELECT MAX(trunc(d_uploaded_date)) d_uploaded_date
FROM dc_modelupload_messages
WHERE v_service_id NOT IN ('EXT') AND v_status_msg = 'SUCCESS'
) mm ON cdw.d_record_start_date >= mm.d_uploaded_date
) failcr ON failcr.cr_id = cro.n_cr_id AND cro.v_status NOT IN ('PUBLISHED', 'CLOSED');
exit;
EOF
)
if [[ $? == 0 ]];then  
CR_DETAILS=$(echo "$CR_DETAILS" | tr '\n\t' ':')
CR_DETAILS=$(echo "$CR_DETAILS" | sed 's/^:*//;s/:*$//')
logMessagePatch "INFO" "CR Details$CR_DETAILS" 
if [[ ! -z $CR_DETAILS && $CR_DETAILS == *"|"* ]];then
    cr_details_arr=${CR_DETAILS//:/ }
    cr_ids=()
    custm_cr_ids=()
    for cr_id_arr in $cr_details_arr
    do
        cr_entry=${cr_id_arr//|/ }
        cr_id=$(echo ${cr_entry%% *} | sed -e 's/^ *//g;s/ *$//g')		
        cr_status=$(echo ${cr_entry#* } | sed -e 's/^ *//g;s/ *$//g')
        cr_status+=($cr_status)
        cr_ids+=($cr_id)
        custm_cr_ids+=("CR-${cr_id}")
    done
    crids_comma_sep=${cr_ids[@]}
    crids_comma_sep="${crids_comma_sep// /,}"
logMessagePatch "INFO" "Getting Custom CRs Names."
CR_NAMES=$(sqlplus -s "/@ISSACT" << EOF
set echo on heading off feedback off SERVEROUTPUT ON trimout on tab off;
SET COLSEP '|'
SELECT LISTAGG(NAME, ':') WITHIN GROUP (ORDER BY ACTION_ID) AS result FROM DG_ACTION_B WHERE ACTION_ID IN ($crids_comma_sep);
exit;
EOF
)
logMessagePatch "INFO" "Custom CRs Names fetched."
CR_NAMES_ARR=$(echo "$CR_NAMES" | tr '\n\t' ':')
CR_NAMES_ARR=$(echo "$CR_NAMES_ARR" | sed 's/^:*//;s/:*$//')
cr_names=${CR_NAMES_ARR//:/,}     
logMessagePatch "INFO" "CR Names $cr_names"  
IFS=',' read -ra cr_names <<< "$cr_names"
for i in "${!cr_names[@]}"; do
    details+="\"${cr_names[$i]}(CR-${cr_ids[$i]})\","
done
details=$(echo "$details" | tr '\n\t' ',')
logMessagePatch "ERROR" "Precheck Failed. Cannot proceed with upgrade.\nAs custom $details CRs are in incomplete/invalid state.  \nReach out to product team for next set of steps.. Exiting Upgrade."  
exit -1   
else 
    logMessagePatch "INFO" "No Pending/Failed CR present. Proceeding for Upgrade."  
fi
else
  logMessagePatch -e "FATAL" "Unable to retrieve CR details from DB(CATMETA/ISSACT), exiting Upgrade.."
  exit -1
fi
logMessagePatch "INFO" "Custom CRs Validation Complete."
}


domainDepPrecheck(){

	logMessagePatch "INFO" "Starting precheck for Domain Deployment"
	download_wallet "FSGBU-DFCS"
	
config_details=$(sqlplus -s "/@DFCSMETA" << EOF
set echo on heading off  feedback off SERVEROUTPUT ON trimout on tab off;
select LISTAGG(TRIM(KEY_VAL),'|') from (SELECT  1 ORDR_BY, DCP.V_PARAM_CODE || ':'|| DCP.V_PARAM_VALUE  KEY_VAL FROM DIA_CONF_PARAMS DCP WHERE V_PARAM_CODE='SCRIPTS_VERSION' UNION select 2 ORDR_BY, PROC_NAME  || ':'|| PROC_STATUS KEY_VAL from AF_CONFIGURATION_STATUS WHERE PROC_STATUS='N') ORDER BY ORDR_BY ASC;
exit;
EOF
)  
if [[ $? == 0 ]];then  
  config_details=$(echo $config_details | sed "s/[ \t]//g")
  logMessagePatch "INFO" "configuration Details=$config_details"
  config_details_arr=${config_details//|/ }
  APP_VERSION=0.0.0
  IS_DOMAIN_DEPLOYED="Y"
  for config in $config_details_arr
    do
      config_entry=${config//:/ }
      key=$(echo ${config_entry%% *} | sed -e 's/^ *//g;s/ *$//g')		
      value=$(echo ${config_entry#* } | sed -e 's/^ *//g;s/ *$//g')
      if [[ $key == "SCRIPTS_VERSION" ]];then
        APP_VERSION=$value
      elif [[ $value == "N" ]];then
        IS_DOMAIN_DEPLOYED="N"
        break;
      fi		
    done
    logMessagePatch "INFO" "APP_VERSION=$APP_VERSION, IS_DOMAIN_DEPLOYED=$IS_DOMAIN_DEPLOYED" 
    if [[ $IS_DOMAIN_DEPLOYED != "Y" ]];then
      logMessagePatch "ERROR" "Precheck Failed. Domain deployment is not complete. Exiting setup."  
      exit -1   
    fi
else
  logMessagePatch "FATAL" "Unable to retrieve configuration details from DB(DFCSMETA), exiting setup.."
  exit -1
fi

}


initialize(){
  basedir=$(pwd)
  artstore -s -e k8s -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY} file pull ${ARTSTORE_PATH}/common_functions.sh ./ -r -f 
  chmod +x $basedir/common_functions.sh
  source $basedir/common_functions.sh   
}

#Start Execution
initialize
crPrecheck
#domainDepPrecheck
