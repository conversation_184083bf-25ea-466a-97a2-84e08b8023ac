#!/bin/python3
import os
import sys
import time
import json
import base64
import requests
import subprocess
from pathlib import Path
import datetime
from requests.exceptions import HTTPError, Timeout, ConnectionError
from requests.auth import HTTPBasicAuth

def logMessage(severity, messagebody):
    datelog = datetime.datetime.now().strftime('%Y-%m-%d')
    timestamplog = datetime.datetime.now().strftime('%H:%M:%S')

    color_reset = "\033[0m" 
    color_info = "\033[1;32m"  # Green for INFO
    color_warning = "\033[1;38;5;214m"  # Orange/yellow for WARN/WARNING check once
    color_error = "\033[1;31m"  # Red for ERROR/FATAL
    severity_upper = severity.upper()

    if severity_upper == "INFO":
        color = color_info
    elif severity_upper in ["WARN", "WARNING"]:
        color = color_warning
    elif severity_upper in ["ERROR", "FATAL"]:
        color = color_error
    else:
        color = ""

    print(f"{color}{datelog} {timestamplog} -- {severity} -- {messagebody}{color_reset}")

def is_empty_error(variables):
    #check_falsy(variables)        Logs Error and exits for None, "", [],{},0,False    
    empty_keys = [key for key, value in variables.items() if not value]
    if empty_keys:
        error_message = f"Mandatory execution params can not be empty: {', '.join(empty_keys)}"
        logMessage("ERROR", f"Validation Error: {error_message}")
        sys.exit(1)

def remove_keys_from_json(data: dict, keys_to_remove: list) -> dict:
    for key in keys_to_remove:
        if key in data:
            del data[key]
    return data

def load_properties(file_path):
    properties = {}
    with open(file_path, "r") as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith("#"):  # Skip empty lines and comments
                continue
            if "=" in line:  # Process only lines with key=value
                key, value = line.split("=", 1)  # Split at the first '='
                properties[key.strip()] = value.strip()  # Remove spaces around key and value
    return properties

             
def request_metadata_api(method, url, headers=None, data_file=None, params=None):
    method = method.upper()
    json_payload = None
    if method == 'POST' and data_file:
        with open(data_file, 'r') as file:
            json_payload = json.load(file)

    response = requests.request(method, url, headers=headers, json=json_payload, params=params)
    return response.status_code, response.json()

def run_shell_command(command: str) -> str:
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        logMessage("ERROR", f"Error executing command: {e}")
        sys.exit(1)

def make_api_request(
    url_path: str,
    method: str = "GET",
    payload: dict = None,
    client_id: str = None,
    client_secret: str = None,
    custom_headers: dict = None,
    api_poll_count: int = 3,
    api_poll_interval: int = 5  # Sleep time between attempts in seconds
) -> dict:
    # Default headers
    headers = {"Content-Type": "application/json"}
    auth = None

    # Add Basic Authentication header if client_id and client_secret are provided
    if client_id and client_secret:
        auth = HTTPBasicAuth(client_id, client_secret)

    # Add custom headers if provided
    if custom_headers:
        headers.update(custom_headers)

    for attempt in range(api_poll_count):
        try:

            if headers.get("Content-Type") == "application/x-www-form-urlencoded":
                payload_key = "data"  # Use form-encoded
            else:
                payload_key = "json"  # Use JSON-encoded

            # Select the appropriate request method
            if method.upper() == "GET":
                response = requests.get(url_path, headers=headers, auth=auth, verify=False, timeout=10)
            elif method.upper() == "POST":
                response = requests.post(url_path, headers=headers, auth=auth,  **{payload_key: payload}, verify=False, timeout=10)
            elif method.upper() == "PUT":
                response = requests.put(url_path, headers=headers, auth=auth,  **{payload_key: payload}, verify=False, timeout=10)
            else:
                logMessage("ERROR", f"Unsupported HTTP method: {method}")
                #sys.exit(1)
                raise Exception(f"Unsupported HTTP method: {method}")

            # Raise an error for bad HTTP status codes
            response.raise_for_status()
            #print(response.json())
            # Parse and return JSON data if successful
            return response.json()
        except Timeout:
            logMessage("WARNING", f"Request timed out (Attempt {attempt + 1}/{api_poll_count}). Retrying in {api_poll_interval} seconds...")
        except ConnectionError:
            logMessage("ERROR", f"Connection refused or unreachable (Attempt {attempt + 1}/{api_poll_count}). Retrying in {api_poll_interval} seconds...")
        except HTTPError as http_err:
            status_code = http_err.response.status_code if http_err.response else "Unknown"
            error_message = f"HTTPError occurred: {http_err}"            
            if http_err.response is not None:
                try:
                    # Capture additional details from the response
                    error_details = http_err.response.json()
                    error_message += f" | Response: {error_details}"
                except ValueError:
                    # If response body is not JSON, include raw text
                    error_message += f" | Response Text: {http_err.response.text}"
            logMessage("ERROR", error_message)

            if status_code >= 500:
                logMessage("WARNING", f"Server error ({status_code}): Retrying after server error (Attempt {attempt + 1}/{api_poll_count})...")
            else:
                raise  # Re-raise for client errors (4xx)
        
        except Exception as e:
            logMessage("ERROR", f"An unexpected error occurred: {e}")
        # If we're not on the last attempt, wait before retrying
        if attempt < api_poll_count - 1:
            time.sleep(api_poll_interval)

    # If we've exhausted retries, exit with an error
    logMessage("ERROR", "Exceeded maximum retry attempts.")
    raise Exception("API request failed after multiple attempts.")

def get_sat() -> str:
    #Fetches the SAT from Kubernetes secrets.
    sat_command = "kubectl get secrets reserved-service-access-token -o jsonpath='{.data.sat}' | base64 -d"
    return run_shell_command(sat_command)

def get_env_ocid(tenant_id: str, msp_proj: str) -> str:
    #Fetches the ENV_OCID from Kubernetes secrets.
    env_ocid_command = (
        f"kubectl -n {msp_proj}--{tenant_id} get secrets configuration.properties -ojson "
        f"--ignore-not-found=true 2>/dev/null | jq -r '.data.\"configuration.properties\"' | base64 -d "
        f"| grep ENV_OCID | cut -d '=' -f2"
    )
    return run_shell_command(env_ocid_command)

def get_erf_token(base_secret_url: str, sat: str) -> str:
    # Retrieves the ERF token using client credentials from a secure endpoint.
    erf_cred_s3_path = f"{base_secret_url}/erf-credentials"
    custom_headers = {"Content-Type": "application/json", "Authorization": f"Bearer {sat}"}
    try:
        erf_idcs_token_details = make_api_request(erf_cred_s3_path, method="GET", custom_headers=custom_headers)        
        idcs_host_url = erf_idcs_token_details.get("idcs_host_url").rstrip('/') + "/oauth2/v1/token"
        idcs_client_id = erf_idcs_token_details.get("idcs_client_id")
        erf_scope = erf_idcs_token_details.get("scope")
        erf_grant_type = erf_idcs_token_details.get("grant_type")
        idcs_client_secret = base64.b64decode(erf_idcs_token_details.get("idcs_client_secret")).decode("utf-8").strip()
        
        s3_data_to_log = json.dumps(remove_keys_from_json(erf_idcs_token_details, ['idcs_client_secret', 'password']), indent=4)
        logMessage("LOG", f"ERF details from S3: \n{s3_data_to_log}")
        
        if not erf_grant_type:
            erf_grant_type = "client_credentials"
        if not erf_scope:
            erf_scope = "erfplatform:read"

        erf_payload = {"scope": erf_scope, "grant_type": erf_grant_type}
        url_enc_headers = {"Accept": "application/json", "Content-Type": "application/x-www-form-urlencoded"}
        erf_token_details = make_api_request(idcs_host_url, method="POST", payload=erf_payload, client_id=idcs_client_id, client_secret=idcs_client_secret, custom_headers=url_enc_headers)
        access_token = erf_token_details.get("access_token")
        
        if not access_token:
            logMessage("ERROR", f"Failed to retrieve access token. ERF token details response: {erf_token_details}")
            raise Exception(f"Failed to retrieve access token. ERF token details response: {erf_token_details}") 
        
        return access_token

    except Exception as e:
        logMessage("ERROR", f"An error occurred while retrieving the ERF token: {e}")
        sys.exit(1)


def get_adb_ocid(tenant_id: str, msp_proj: str) -> str:
    adb_ocid_command=f"kubectl -n {msp_proj}--{tenant_id} get secrets dbaas-details -ojson | jq -r '.data.ADB_INSTANCE_OCID' | base64 -d"
    return run_shell_command(adb_ocid_command)

def get_fsafnd_service_url(tenant_id: str, service_key: str) -> str:
    service_key_command = (
        f"kubectl -n fsgbu-fsafnd--{tenant_id} get secrets service-properties "
        f"-o jsonpath='{{.data.service-properties\\.props}}' | base64 -d | grep -w {service_key} | cut -d '=' -f2"
    )
    return run_shell_command(service_key_command).strip()

