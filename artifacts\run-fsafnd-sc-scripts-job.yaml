---
apiVersion: batch/v1
kind: Job
metadata:
  name: run-fsafnd-sc-__PATCH_ID_LOWER__
spec:
  backoffLimit: 1
  # ttlSecondsAfterFinished: 300
  template:
    metadata:
      annotations:
        version: v1.0
      labels:
        component: run-fsafnd-sc-__PATCH_ID_LOWER__
    spec:
      containers:
        - image: {{REGISTRY}}/{{PREFIX}}-fsgbu-fsafnd/dfcs-fsafnd-schemacreator:__TAG__
          name: run-fsafnd-sc-__PATCH_ID_LOWER__
          imagePullPolicy: Always
          env:
            - name: PDB_NAME
              value: "__TENANT_PDB_STRING__"
            - name: TENANT_ID
              value: __TENANT_ID__
            - name: SERVICES
              value: "__SERVICE_ID_LIST__"
            - name: WALLET_ENABLED
              value: "true"
            - name: CONFIGURE_WALLET
              value: "true"
            - name: CURRENT_CLUSTER
              value: "__CURRENT_CLUSTER__"
            - name: WTSS_URL
              value: "__WTSS_URL__"
            - name: DEFAULT_TIMEZONE
              value: "__DEFAULT_TIMEZONE__"
            - name: CONFIG_SCHEMA_HOST
              value: "FSGBU-FSAFND"
            - name: PUSH_TO_SHARED_BUCKET
              value: "__PUSH_TO_SHARED_BUCKET__"
            - name: STREAM_POOL_ID
              value: "__STREAM_POOL_ID__"
            - name: REGION
              value: "__REGION__"
            - name: wallet_location
              value: "/opt/wallet/__CURRENT_CLUSTER__"
            - name: CREATE_ORDER
              value: "__CREATE_ORDER__"
            - name: BASE_SECRET_URLS
              value: "__BASE_SECRET_URLS__"
            - name: WALLET_PATH
              value: "__BASE_SECRET_URLS____TENANT_ID__"
            - name: IS_ADW
              value: "true"
            - name: SECURE_STORE_ENABLED
              value: "true"
            - name: TENANT_INFO
              value: "/home/<USER>/tenant.json"
            - name: REQUEST_ID
              value: "__REQUEST_ID__"
            - name: SETUP_INFO_NAME
              value: "__SETUP_INFO_NAME__"
            - name: DISABLE_DEPENDENCIES
              value: "__DISABLE_DEPENDENCIES__"
            - name: EXECUTE_CHANGELOGSYNC
              value: "__EXECUTE_CHANGELOGSYNC__"
          command:
            - 'sh'
            - '-c'
            - '/home/<USER>/wrapper/schema-creation.sh'
          volumeMounts:
            - name: oci-schema-creation-volume
              mountPath: /home/<USER>/wrapper
            - name: oci-schema-creation-config-volume
              mountPath: /opt/schemacreator/pipeline.env
              subPath: pipeline.env
            - name: stream-volume
              mountPath: /opt/schemacreator/stream.env
              subPath: stream.env
            - name: sat-vol
              mountPath: /opt/sat/token
              subPath: sat
            - name: oci-vol
              mountPath: /os_parameters
      restartPolicy: Never
      serviceAccountName: namespace-admin
      securityContext:
        runAsUser: 1000
      volumes:
      - name: oci-schema-creation-volume
        configMap:
          defaultMode: 0777
          name: oci-schema-creator-cm
      - name: oci-schema-creation-config-volume
        configMap:
          defaultMode: 0777
          name: pipeline-config
      - name: stream-volume
        configMap:
          defaultMode: 0777
          name: current-active-stream
      - name: sat-vol
        secret:
          secretName: reserved-service-access-token
      - name: oci-vol
        secret:
          secretName: reserved-object-store-entity
