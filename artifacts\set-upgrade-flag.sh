#!/bin/bash
set -e

chmod +x ./artstore
chmod +x ./common_functions.sh
source ./common_functions.sh

replaceParams(){
    taskName=${1}
	echo "Task Name : $taskName"
sed -i \
        -e "s|{{PREFIX}}|${PREFIX}|g" \
        -e "s|{{REGISTRY}}|${REGISTRY}|g" \
        -e "s|__artstore_path__|${ARTSTORE_PATH}|g" \
        -e "s|__deployments_artifacts_path__|${deployments_artifacts_path}|g" \
        -e "s|__PATCH_STAGE__|${PATCH_STAGE}|g" \
        -e "s|__PATCH_ID__|${PATCH_ID}|g" \
        -e "s|__REGION__|${oci_region}|g" \
        -e "s|__REGION_CODE__|${REGION_CODE}|g" \
        -e "s|__TENANCY__|${TENANCY}|g" \
        -e "s|__WALLET_PATH__|${BASE_SECRET_URLS}${TENANT_ID}|g" \
        -e "s|__TENANT_ID__|${TENANT_ID}|g" \
        -e "s|__BASE_SECRET_URLS__|${BASE_SECRET_URLS}|g" \
        -e "s|__ADW__|true|g" \
        -e "s|__MSP_PROJ_NAME__|${MSP_PROJ_NAME}|g" \
		-e "s|__TASK_NAME__|${taskName}|g" \
        ./dfcs-deployment-job-wrapper.yaml
		
		cat ./dfcs-deployment-job-wrapper.yaml

}

revertTaskName(){
    taskName=${1}
	echo "Reverting Task Name : $taskName"
sed -i \
		-e "s|${taskName}|__TASK_NAME__|g" \
        ./dfcs-deployment-job-wrapper.yaml
		
		cat ./dfcs-deployment-job-wrapper.yaml
}

deployment_upgrading_flag_set(){
	chmod +x dfcs_deployment_job_wrapper.sh
	logprefix=`logMessagePatch "INFO" ""`
	replaceParams "SET_UPGRADED_FLAG"
	echo "sub_namespace----- : ${SUB_NAMESPACE}"
	kubectl -n  "${SUB_NAMESPACE}" delete job dfcs-deployment-exec-job --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" delete configmap dfcs-deployment-job-wrapper --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" create configmap dfcs-deployment-job-wrapper --from-file=dfcs_deployment_job_wrapper.sh
	k8s_create_job "${SUB_NAMESPACE}" "dfcs-deployment-exec-job" "dfcs-deployment-job-wrapper.yaml"
	result=$(kubectl -n ${SUB_NAMESPACE}  get pods --selector=job-name="dfcs-deployment-exec-job" --output=jsonpath='{.items[0].status.phase}')
	if [[ "${result^^}" == "FAILED" ]]; then
		exit -1
	fi
	revertTaskName "SET_UPGRADED_FLAG"
}


#Execution Starts 
patch::initialize
deployment_upgrading_flag_set
