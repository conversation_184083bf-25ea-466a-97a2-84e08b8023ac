#!/bin/bash

set ${DEBUG:-+x}
set -e

mkdir -p $TENANT_ID
rm -rf $TENANT_ID/*

dis::constructPayload(){
	
	local artifact_version="$1"
	local OAS_CLIENT_DISPLAY_NAME=oas-"$APP_NAME"-"$TENANT_ID"
	if [[ ! -z "$oci_dr_region" && "$REGION_CODE" == "phx" ]];then
		local OAS_CLIENT_DISPLAY_NAME="oas-${APP_NAME}"-"${TENANT_ID}-dr"
	fi
	local idcsEndpoint='/admin/v1/Apps/.search'
	WTSS_IDCS_HOST=$(kubectl -n ${SUB_NAMESPACE} get secret tenant-order -o jsonpath=\{.data."WTSS_IDCS_HOST"\} | base64 -d)
	local IDCS_API_URL=${WTSS_IDCS_HOST}${idcsEndpoint}
	local IDCS_TOK=$(_get_idcs_token)
	if [[ -z "$IDCS_TOK" ]];then
		logMessagePatch "ERROR" "IDCS Token returned empty.. Tenant Onboarding skipped. Please try after some time."
		exit 1
	fi
	
	local appDetails=$(_getIDCSAppDetails "$IDCS_API_URL" "$IDCS_TOK" "$OAS_CLIENT_DISPLAY_NAME")
	local idcsClientId=$(jq -r '.Resources[0].name | select(.!=null)' <<< "$appDetails")
	local applicationId=$(jq -r '.Resources[0].id | select(.!=null)' <<< "$appDetails")
	local idcsClientSecret=$(jq -r '.Resources[0].clientSecret | select(.!=null)' <<< "$appDetails")	
	logMessagePatch "INFO" "IDCS details : idcsClientId=$idcsClientId,applicationId=$applicationId,idcsClientSecret=$idcsClientSecret"

if [[ -z $idcsClientId ]];then
	dis_idcs_secret_exists=$(kubectl -n ${SUB_NAMESPACE} get secret dis-idcs -o jsonpath='{.data.idcsClientId}' --ignore-not-found=true 2>/dev/null | base64 -d)
	if [[ -z $dis_idcs_secret_exists ]];then
		logMessagePatch "ERROR" "IDCS Secrets for DIS_APP not found - dis-idcs secret missing in environment."
		exit -1
	fi
	logMessagePatch "INFO" "Get idcsClientId from dis-idcs secret.."
	idcsClientId=$(kubectl -n ${SUB_NAMESPACE} get secret dis-idcs -o jsonpath='{.data.idcsClientId}' | base64 -d)
fi
if [[ -z $applicationId ]];then
	dis_idcs_secret_exists=$(kubectl -n ${SUB_NAMESPACE} get secret dis-idcs -o jsonpath='{.data.applicationId}' --ignore-not-found=true 2>/dev/null | base64 -d)
	if [[ -z $dis_idcs_secret_exists ]];then
		logMessagePatch "ERROR" "IDCS Secrets for DIS_APP not found - dis-idcs secret missing in environment."
		exit -1
	fi
	logMessagePatch "INFO" "Get applicationId from dis-idcs secret.."
	applicationId=$(kubectl -n ${SUB_NAMESPACE} get secret dis-idcs -o jsonpath='{.data.applicationId}' | base64 -d)
fi
if [[ -z $idcsClientSecret ]];then
	dis_idcs_secret_exists=$(kubectl -n ${SUB_NAMESPACE} get secret dis-idcs -o jsonpath='{.data.idcsClientSecret}' --ignore-not-found=true 2>/dev/null | base64 -d)
	if [[ -z $dis_idcs_secret_exists ]];then
		logMessagePatch "ERROR" "IDCS Secrets for DIS_APP not found - dis-idcs secret missing in environment."
		exit -1
	fi
	logMessagePatch "INFO" "Get idcsClientSecret from dis-idcs secret.."	
	idcsClientSecret=$(kubectl -n ${SUB_NAMESPACE} get secret dis-idcs -o jsonpath='{.data.idcsClientSecret}' | base64 -d)	
fi

python <<END
import json
import os
data={}
deploymentTemplate=['visualization']
visualization={}
biRoles={}
dv={}
oasObj={}
oas=[]
products=[]
## custom roles
cust_roles=[]
DFCSDVAdmin={}
DFCSDVAdmin_dflt={}
DFCSDataAnalyst={}
DFCSDataAnalyst_dflt={}
DFCSSeniorDataAnalyst={}
DFCSSeniorDataAnalyst_dflt={}
DFCSSeniorMgmtExec={}
DFCSSeniorMgmtExec_dflt={}
DFCSRiskOfficers={}
DFCSRiskOfficers_dflt={}
DFCSIntAnalyst={}
DFCSIntAnalyst_dflt={}
DFCSSeniorIntAnalyst={}
DFCSSeniorIntAnalyst_dflt={}
DFCSQualityControl={}
DFCSQualityControl_dflt={}
DFCSReconAnalyst={}
DFCSReconAnalyst_dflt={}
DFCSRTFAdmin={}
DFCSRTFAdmin_dflt={}
DFCSDataSteward={}
DFCSDataSteward_dflt={}

customRoles={}

#DFCSDVAdmin
DFCSDVAdmin["appRoleName"]="DFCSDVAdmin"
DFCSDVAdmin["principalName"]="DFCS-DVADMIN"
cust_roles.append(DFCSDVAdmin)

#DFCSDVAdmin_dflt
DFCSDVAdmin_dflt["appRoleName"]="DVContentAuthor"
DFCSDVAdmin_dflt["principalName"]="DFCS-DVADMIN"
cust_roles.append(DFCSDVAdmin_dflt)

#DFCSDataAnalyst
DFCSDataAnalyst["appRoleName"]="DFCSDataAnalyst"
DFCSDataAnalyst["principalName"]="DFCS-DATANALYST"
cust_roles.append(DFCSDataAnalyst)

#DFCSDataAnalyst_dflt
DFCSDataAnalyst_dflt["appRoleName"]="DVContentAuthor"
DFCSDataAnalyst_dflt["principalName"]="DFCS-DATANALYST"
cust_roles.append(DFCSDataAnalyst_dflt)

#DFCSSeniorDataAnalyst
DFCSSeniorDataAnalyst["appRoleName"]="DFCSSeniorDataAnalyst"
DFCSSeniorDataAnalyst["principalName"]="DFCS-SENIORDATANALYST"
cust_roles.append(DFCSSeniorDataAnalyst)

#DFCSSeniorDataAnalyst_dflt
DFCSSeniorDataAnalyst_dflt["appRoleName"]="DVContentAuthor"
DFCSSeniorDataAnalyst_dflt["principalName"]="DFCS-SENIORDATANALYST"
cust_roles.append(DFCSSeniorDataAnalyst_dflt)

#DFCSSeniorMgmtExec
DFCSSeniorMgmtExec["appRoleName"]="DFCSSeniorMgmtExec"
DFCSSeniorMgmtExec["principalName"]="DFCS-SENIORMGMTEXEC"
cust_roles.append(DFCSSeniorMgmtExec)

#DFCSSeniorMgmtExec_dflt
DFCSSeniorMgmtExec_dflt["appRoleName"]="DVConsumer"
DFCSSeniorMgmtExec_dflt["principalName"]="DFCS-SENIORMGMTEXEC"
cust_roles.append(DFCSSeniorMgmtExec_dflt)

#DFCSRiskOfficers
DFCSRiskOfficers["appRoleName"]="DFCSRiskOfficers"
DFCSRiskOfficers["principalName"]="DFCS-RISKOFFICERS"
cust_roles.append(DFCSRiskOfficers)

#DFCSRiskOfficers_dflt
DFCSRiskOfficers_dflt["appRoleName"]="DVConsumer"
DFCSRiskOfficers_dflt["principalName"]="DFCS-RISKOFFICERS"
cust_roles.append(DFCSRiskOfficers_dflt)

#DFCSIntAnalyst
DFCSIntAnalyst["appRoleName"]="DFCSIntAnalyst"
DFCSIntAnalyst["principalName"]="DFCS-INTANALYST"
cust_roles.append(DFCSIntAnalyst)


#DFCSIntAnalyst_dflt
DFCSIntAnalyst_dflt["appRoleName"]="DVContentAuthor"
DFCSIntAnalyst_dflt["principalName"]="DFCS-INTANALYST"
cust_roles.append(DFCSIntAnalyst_dflt)

#DFCSSeniorIntAnalyst
DFCSSeniorIntAnalyst["appRoleName"]="DFCSSeniorIntAnalyst"
DFCSSeniorIntAnalyst["principalName"]="DFCS-SENIORINTANALYST"
cust_roles.append(DFCSSeniorIntAnalyst)

#DFCSSeniorIntAnalyst_dflt
DFCSSeniorIntAnalyst_dflt["appRoleName"]="DVContentAuthor"
DFCSSeniorIntAnalyst_dflt["principalName"]="DFCS-SENIORINTANALYST"
cust_roles.append(DFCSSeniorIntAnalyst_dflt)

#DFCSQualityControl
DFCSQualityControl["appRoleName"]="DFCSQualityControl"
DFCSQualityControl["principalName"]="DFCS-QUALITYCONTROL"
cust_roles.append(DFCSQualityControl)

#DFCSQualityControl_dflt
DFCSQualityControl_dflt["appRoleName"]="DVContentAuthor"
DFCSQualityControl_dflt["principalName"]="DFCS-QUALITYCONTROL"
cust_roles.append(DFCSQualityControl_dflt)

#DFCSReconAnalyst
DFCSReconAnalyst["appRoleName"]="DFCSReconAnalyst"
DFCSReconAnalyst["principalName"]="DFCS-RECONANALYST"
cust_roles.append(DFCSReconAnalyst)

#DFCSReconAnalyst_dflt
DFCSReconAnalyst_dflt["appRoleName"]="DVContentAuthor"
DFCSReconAnalyst_dflt["principalName"]="DFCS-RECONANALYST"
cust_roles.append(DFCSReconAnalyst_dflt)

#DFCSRTFAdmin
DFCSRTFAdmin["appRoleName"]="DFCSRTFAdmin"
DFCSRTFAdmin["principalName"]="DFCS-RTFADMIN"
cust_roles.append(DFCSRTFAdmin)

#DFCSRTFAdmin_dflt
DFCSRTFAdmin_dflt["appRoleName"]="DVConsumer"
DFCSRTFAdmin_dflt["principalName"]="DFCS-RTFADMIN"
cust_roles.append(DFCSRTFAdmin_dflt)

#DFCSDataSteward
DFCSDataSteward["appRoleName"]="DFCSDataSteward"
DFCSDataSteward["principalName"]="DFCS-DATASTEWARD"
cust_roles.append(DFCSDataSteward)

#DFCSDataSteward_dflt
DFCSDataSteward_dflt["appRoleName"]="DVConsumer"
DFCSDataSteward_dflt["principalName"]="DFCS-DATASTEWARD"
cust_roles.append(DFCSDataSteward_dflt)

customRoles=cust_roles


productsObj={}
productsObj['name'] = "fsafnd"
productsObj['version'] = "$artifact_version"
products.append(productsObj)

connectionPool=json.loads("""$schema_details_json""")
dvConnectionsArr=json.loads("""$dvJson""")
data['applicationId'] = "$applicationId"
data['idcsClientId'] = "$idcsClientId"
data['idcsClientSecret'] = "$idcsClientSecret"
data['idcsUrl'] = "$WTSS_IDCS_HOST"
#data['disPlatformVersion'] = "24.6.1"
data['products']=products
data['deploymentTemplate'] = deploymentTemplate
biRoles['BIAdministrator'] = "$biadmin_user"
biRoles['BIConsumer'] = '$APP_NAME_UPPER'+'-BIConsumer'
biRoles['BIContentAuthor'] = '$APP_NAME_UPPER'+'-BIContentAuthor'
biRoles['DVConsumer'] = '$APP_NAME_UPPER'+'-DVConsumer'
biRoles['DVContentAuthor'] = '$APP_NAME_UPPER'+'-DVContentAuthor'

biRoles["customRoles"]=customRoles

oasObj['biRoles'] = biRoles
oasObj['connectionPool'] = connectionPool
oasObj['isBIPEnabled'] = True
oasObj['isAnalyticsEnabled'] = True
oasObj['deployRpd'] = True
oasObj['isDVEnabled'] = True
oasObj['oasProductVersion'] = '6.4'
dv['connections'] = dvConnectionsArr
oasObj['dv'] = dv
oas.append(oasObj)
visualization['oas'] = oas
data['visualization'] = visualization
with open('$TENANT_ID/dis_tenant_payload.json', 'w') as outfile:
	json.dump(data, outfile, indent=2,sort_keys=True)
END

logMessagePatch "INFO" "PAYLOAD for GBU_ID-$TENANT_ID, DIS_TENANT_ID-$DIS_TENANT_ID is--"
cat $TENANT_ID/dis_tenant_payload.json | jq .

}

# Prepare Connection Pool Json
dis::prepareConPoolJson(){
local dummy_json_file="$1"
python <<END
import json
import os
dummy=[]
ds={}
ds_init={}

ds['name'] = '$temp_datasource_name'
ds['dbUser'] = '$temp_schema_name'
#ds['dbPassword'] = '$temp_schema_password'
#ds['connectionString'] = '$CONN_STRING'
ds['directConnectName'] = '$DC_NAME_CLUSTER'
dummy.append(ds)

ds_init['name'] = '$temp_datasource_name'+'_INIT'
ds_init['dbUser'] = '$temp_schema_name'
#ds_init['dbPassword'] = '$temp_schema_password'
#ds_init['connectionString'] = '$CONN_STRING'
ds_init['directConnectName'] = '$DC_NAME_CLUSTER'
dummy.append(ds_init)
if '$schemaFileExists' == 'true':
	with open('$dummy_json_file', 'r') as outfile:
		loaded=json.load(outfile)
	loaded.append(ds)
	loaded.append(ds_init)
else:
	loaded=dummy

with open('$dummy_json_file', 'w') as outfile:
	json.dump(loaded, outfile)
END
}

dis::upgradeTenantVersion() {
artifact_version="$1"
input_dis_platform_version=$DIS_Platform_Version
oas_access_tok=$(dis::getAccessToken)
upgrade_dis_json='{"tenants":["'$DIS_TENANT_ID'"],"products":[{"name":"fsafnd","version":"'$DIS_ARTIFACT_VERSION'"}]}'

dis_tenant_details=$(dis::getDISTenantDetails "$oas_access_tok" "$DIS_TENANT_ID")
#echo "dis_tenant_details---$dis_tenant_details"
dis_tenant_status=$(echo "$dis_tenant_details" | jq -r '.status')

oasProductVersion=$(echo $dis_tenant_details | jq -r '.oasProductVersion')
disPlatformVersion=$(echo $dis_tenant_details | jq -r '.disPlatformVersion')
dis_platform_ver=$(dis::getDISPlatformVersions "$oas_access_tok")
latestPlatformVer=$(echo "$dis_platform_ver" | jq -r '.latestVersion') 
availablePlatformVers=$(echo "$dis_platform_ver" | jq -r '.availableVersions') 

logMessagePatch "INFO" "DIS_PLATFORM_VERSION latest   : $latestPlatformVer"
logMessagePatch "INFO" "DIS_PLATFORM_VERSION of existing tenant   : $disPlatformVersion"
logMessagePatch "INFO" "DIS_PLATFORM_VERSION from configmap    : $input_dis_platform_version"

version_exists=0
version_exists=$(echo $availablePlatformVers |jq --arg input_dis_platform_version "$input_dis_platform_version" '[.[] | select(. ==$input_dis_platform_version)] | length')

logMessagePatch "INFO" "DIS_PLATFORM_VERSION validation"
if [[ $version_exists -gt 0 ]]; then
	if [[ "$input_dis_platform_version" == "$disPlatformVersion" ]]; then
		logMessagePatch "INFO" " DIS_PLATFORM_VERSION is matching with existing DIS_PLATFORM_VERSION of tenant "
		logMessagePatch "INFO" "upgrade_dis_json: $upgrade_dis_json"
		result=$(dis::upgradeDisTenant "$oas_access_tok" "PUT" "$DIS_TENANT_ID" "$upgrade_dis_json")
	else
		upgrade_dis_json=$(echo $upgrade_dis_json | jq -r '. += {"disPlatformVersion":"'$input_dis_platform_version'"}')
		logMessagePatch "INFO" "dis will be using DIS_PLATFORM_VERSION=$input_dis_platform_version"
		logMessagePatch "INFO" "upgrade_dis_json: $upgrade_dis_json"	
		result=$(dis::upgradeDisTenant "$oas_access_tok" "PUT" "$DIS_TENANT_ID" "$upgrade_dis_json")

	fi
else 
	logMessagePatch "ERROR" "DIS platform version read form configfile is not supported version, please correct it "	
	exit 1
fi

echo "Status -- result=$result"
status=$(jq -r '.status' <<< "$result")
message=$(jq -r '.message' <<< "$result")
if [[ ${result} == *'"error":'* ]];then 		
	logMessagePatch "ERROR" "$message"
	exit -1
else 		
	requestId=$(jq -r '.requestId' <<< "$result")
fi

logMessagePatch "INFO" "DIS Submitted-status=$status ,requestId= $requestId"
logMessagePatch "INFO" "JOB TIMEOUT=$((oas_api_status_timeout / 60)) minutes"
countdown=0
sleep_time=$oas_api_status_sleep_time
timeout=$oas_api_status_timeout
retry=$((timeout / sleep_time))	
logMessagePatch "INFO" "Poll Upgrade JOB status: Pending"
sleep $sleep_time

while [[ true ]]
do
(( countdown=countdown+1 ))
if [[ $countdown -gt $retry ]];then
	logMessagePatch "ERROR" "Poll JOB status for Upgrade DIS Tenant-$DIS_TENANT_ID Timed out after $((timeout / 60)) minutes."
	exit 1
else				
	oas_access_tok=$(dis::getAccessToken)			
	reqStatusObj=$(dis::getUpgradeTenantStatus "$oas_access_tok" "$requestId" )	
	#logMessagePatch "INFO" "reqStatusObj=$reqStatusObj"
	if [[ ${reqStatusObj} == *'"Rejected"'* ]];then
		res=$(jq -r --arg DIS_TENANT_ID $DIS_TENANT_ID '.listOfTenants[] | select(.tenantId==$DIS_TENANT_ID)' <<< "$reqStatusObj")
		
		reason=$(jq -r '.reason' <<< "$res")
		status=$(jq -r '.status' <<< "$res")

		if [[ "$reason" == *"already on version"* ]];then
			#result=$(dis::upgradeDisTenant "$oas_access_tok" "GET" )
			#res=$(jq -r --arg DIS_TENANT_ID $DIS_TENANT_ID '.tenants[] | select(.tenantid==$DIS_TENANT_ID)' <<< "$result")
			logMessagePatch "INFO" "DIS tenant already on version, skipping upgrade"
			logMessagePatch "INFO" "Upgrade Status : $status"
			logMessagePatch "INFO" "Reason : $reason"
			break
		else
			logMessagePatch "ERROR" "Skipping Upgrade,status:$status, reason:$reason"
			exit -1
		fi				
	elif [[ "${reqStatusObj^^}" == *"ERROR"* ]];then
		logMessagePatch "ERROR" "Error occured during upgrade, Skipping stage :$reqStatusObj"
		exit -1
	fi		
	result=$(dis::upgradeDisTenant "$oas_access_tok" "GET" )
	echo "Status - result=$result"
	res=$(jq -r --arg DIS_TENANT_ID $DIS_TENANT_ID '.tenants[] | select(.tenantid==$DIS_TENANT_ID)' <<< "$result")
	if [[ "${res^^}" == *'"SUCCESS"'* ]];then
		logMessagePatch "INFO" "Poll JOB status: Successful"
		break
	elif [[ "${res^^}" == *'"STARTED"'* ]];then
		status=$(jq -r '.updateStatus' <<< "$res")
		logMessagePatch "INFO" "Poll Upgrade JOB status: $status"
	else 
		logMessagePatch "INFO" "Poll Upgrade JOB status: Error.. result=$result"
		status=$(jq -r '.status' <<< "$res")
		message=$(jq -r '.message' <<< "$res")
		if [[ ${status} -ge 400 && ${status} -lt 500 ]];then   	
			logMessagePatch "ERROR" "Poll JOB status: Upgrade DIS Tenant($DIS_TENANT_ID) - status:$status, $message"
			break
		elif [[ ${status} -ge 500 ]];then  
			logMessagePatch "ERROR" "Poll JOB status: Retry due to error.. status=${status}"	
		elif [[ "${res^^}" == *'"ERROR":'* ]];then
			break					
		fi	
	fi			
	sleep $sleep_time		
fi
done	
}

dis::prepareBipJson(){
dymmy_bip_Json_file="$1"
python <<END
import json
import os
dummy=[]
bipJson={}

bipJson['name'] = 'ORCE'
bipJson['driverType'] = 'ORACLE11G'
bipJson['driverClass'] = 'oracle.jdbc.OracleDriver'
bipJson['jdbcUrl'] = '$CONN_STRING'

bipJson['systemUser'] = False
bipJson['user'] = "$temp_schema_name"
bipJson['password'] = "$temp_schema_password"

dummy.append(bipJson)
if '$bipjsonFileExists' == 'true':
	with open('$dymmy_bip_Json_file', 'r') as outfile:
		loaded=json.load(outfile)
	loaded.append(bipJson)
else:
	loaded=dummy

with open('$dymmy_bip_Json_file', 'w') as outfile:
	json.dump(loaded, outfile, indent=2, sort_keys=True)
END
}

#Prepare DV Json
dis::prepareDvJson(){
python <<END
import json
import os
dummy=[]
dvJson={}

dvJson['technology'] = 'ADW'
dvJson['connectionName'] = '$temp_datasource_name'
#dvJson['connectionDescription'] = '$temp_datasource_name'
#dvJson['connectionString'] = '$CONN_STRING'
dvJson['username'] = "$temp_schema_name"
dvJson['directConnectName'] = '$DV_DC_NAME_CLUSTER'
#dvJson['password'] = "$temp_schema_password"

dummy.append(dvJson)
if '$dvJsonFileExists' == 'true':
	with open('$dymmy_dv_json_file', 'r') as outfile:
		loaded=json.load(outfile)
	loaded.append(dvJson)
else:
	loaded=dummy

with open('$dymmy_dv_json_file', 'w') as outfile:
	json.dump(loaded, outfile)
END
}

checkDCSaveStatus(){
local countdown=0
local sleep_time=180
local timeout=1800
local retry=10

while [ true ]
		do
		(( countdown=countdown+1 ))
		if [ $countdown -gt $retry ];then
			logMessagePatch "ERROR" "Direct connect details save timed out after - $((timeout/60)) minutes."		
			exit 1
		fi
			sleep $sleep_time
			oas_access_tok=$(dis::getAccessToken)
			res_dc_save_status_obj=$(curl --insecure -X  "GET" "$DC_URL/status"  -H "Authorization: Bearer $oas_access_tok" -H "Content-Type: application/json")
			echo "Response: $res_dc_save_status_obj"
			res_dc_save_status=$(jq -r '.status' <<< "$res_dc_save_status_obj")
			res_dc_save_msg=$(jq -r '.message' <<< "$res_dc_save_status_obj")	
		if [[ ${res_dc_save_status^^} == "SUCCESS" || ${res_dc_save_msg^^} == *"UPDATED SUCCESSFULLY"* ]];then
			logMessagePatch "INFO" "Direct connect details saved successfully."
			break;
		elif [[ ${res_dc_save_status^^} == "ERROR" || ${res_dc_save_msg^^} == *"FAILED"* || ${res_dc_save_msg^^} == *"ERROR"* ]];then
			logMessagePatch "ERROR" "Direct connect details failed to save."
			logMessagePatch "Response:" "$res_dc_save_status_obj"
			exit 1
		fi
		done
}


checkstatus(){
local countdown=0
local sleep_time=$oas_api_status_sleep_time
local timeout=$oas_api_status_timeout
local retry=$((timeout / sleep_time))	

if [[ "${result}" == *"already a thread performing ONBOARDING"* || "${result}" == *"already a thread performing UPDATE for tenant"* ]];then
	local result=$(dis::onboardTenantAndStatus "GET")
	logMessagePatch "INFO" "Old Onboarding in progess, Fetching Current Status... "
fi
if [[ "${result^^}" == *"SUCCESS"* || "${result^^}" == *"ACCEPTED"* || "${result^^}" == *"IN-PROGRESS"* ]];then	
	while [ true ]
		do
		(( countdown=countdown+1 ))
		if [ $countdown -gt $retry ];then
			logMessagePatch "ERROR" "Poll Status Timeout after - $((timeout/60)) minutes : Rerun the stage to poll status."		
			exit 1
		fi
		sleep $sleep_time
		local result=$(dis::onboardTenantAndStatus "GET")			
		local OASURL=$(jq -r '.status' <<< "$result")	
		if [[ "$OASURL" == "Success"* ]];then
			logMessagePatch "INFO" "Onboarding Successful.."
			logMessagePatch "INFO" "Result: ${result}"
			local oasjson='{"oas":'$(jq -r '.visualizationUrl.oas' <<< "$result")'}'						
			logMessagePatch "INFO" "oasjsonval - ${oasjson}"
			kubectl -n ${SUB_NAMESPACE} create secret generic oasdisurls --from-literal=oasurls="${oasjson}" -o yaml --dry-run=client | kubectl  apply -f -
			break;
		elif [[ "${OASURL^^}" == "ERROR"* ]];then
			logMessagePatch "ERROR" "Onboarding failed.."
			logMessagePatch "ERROR" "${result}"
			break;
		else
			logMessagePatch "INFO" "RESPONSE from Onboarding : $OASURL"		
		fi		
		done
else
	logMessagePatch "ERROR" "Onboarding failed - ${result}"
	exit 1
fi
}

checkDeleteStatus(){
local countdown=0
local sleep_time=$oas_api_status_sleep_time
local timeout=$oas_api_status_timeout
local retry=$((timeout / sleep_time))	
local result="$1"
local deregisterFlag="$2"
if [[ "${result}" == *"there is already a thread performing DELETE"* || "${result}" == *"Delete Operation Started for tenant"* ]];then
	result="IN-PROGRESS"
elif [[ "${result}" == *"is not onboarded yet"* ]];then
	logMessagePatch "WARN" "DIS Tenant Already soft Deleted"
	return
fi
if [[ "${result^^}" == *"SUCCESS"* || "${result^^}" == *"ACCEPTED"* || "${result^^}" == *"IN-PROGRESS"* ]];then	
	while [ true ]
		do
		(( countdown=countdown+1 ))
		if [ $countdown -gt $retry ];then
			logMessagePatch "ERROR" "Poll status timeout after - $((timeout/60)) minutes : Rerun the Step to poll status."		
			exit 1
		fi
		sleep $sleep_time
		oas_access_tok=$(dis::getAccessToken)
		result=$(dis::deleteOASTenantAndStatus "$oas_access_tok" "GET" "$DIS_TENANT_ID" "$deregisterFlag")	
		status=$(jq -r '.status' <<< "$result")
		message=$(jq -r '.message' <<< "$result")
		if [[ "$status" == "Success" ]];then
			if [[ $deregisterFlag == "false" ]];then
				logMessagePatch "INFO" "DIS Tenant Soft Delete Successful.."
			else
				logMessagePatch "INFO" "DIS Tenant Delete Successful.."
			fi	
			local tenantJson=$(dis::getTenantObjDetFrmList "$oas_access_tok" "$DIS_TENANT_ID")		
			logMessagePatch "INFO" "Tenant Json:" "${tenantJson}"
			break;
		elif [[ "${status^^}" == "ERROR"* && "$message" == *"Invalid tenant id"* ]];then
			logMessagePatch "INFO" "DIS Tenant Successfully Deleted."
			break;
		elif [[ "${status^^}" == "ERROR"* ]];then
			logMessagePatch "ERROR" "DIS Tenant Deletion failed, message=$message"
			break;
		else
			logMessagePatch "INFO" "RESPONSE for Delete DIS-Tenant : $status"		
		fi		
		done
elif [[ "${result^^}" == *"ERROR"* || "${result^^}" == *"REJECTED"* ]];then
	message=$(jq -r '.message' <<< "$result")
	logMessagePatch "ERROR" "DIS Tenant Deletion failed, message=$message"
fi
}


checkPayloadUpdateStatus(){
local countdown=0
local sleep_time=$oas_api_status_sleep_time
local timeout=$oas_api_status_timeout
local retry=$((timeout / sleep_time))	
local result="$1"
#logMessagePatch "INFO" "inside status check fn"
logMessagePatch "INFO" "result : $result"
status=$(jq -r '.status' <<< "$result")
messge=$(jq -r '.message' <<< "$result")

logMessagePatch "INFO" "status : $status"
logMessagePatch "INFO" "messge : $messge"

if [[ "${status^^}" == "ACCEPTED"* || "${status^^}" == "SUCCESS"* || "${status^^}" == "IN-PROGRESS"* ]];then
	while [ true ]
		do
		(( countdown=countdown+1 ))
		if [ $countdown -gt $retry ];then
			logMessagePatch "ERROR" "Poll status timeout after - $((timeout/60)) minutes : Rerun the Step to poll status."		
			exit 1
		fi
		sleep $sleep_time
		result=$(dis::payloadUpdateStatus "GET")
		status=$(jq -r '.status' <<< "$result")
		messge=$(jq -r '.message' <<< "$result")

		if [[ "${status^^}" == "SUCCESS"* ]]; then
			logMessagePatch "INFO" "Tenant Paylaod update success"
			logMessagePatch "INFO" "status : $status"
			logMessagePatch "INFO" "messge : $messge"
			local oasjson='{"oas":'$(jq -r '.visualizationUrl.oas' <<< "$result")'}'
			logMessagePatch "INFO" "oasjson : $oasjson"
			kubectl -n ${SUB_NAMESPACE} create secret generic oasdisurls --from-literal=oasurls="${oasjson}" -o yaml --dry-run=client | kubectl  apply -f -
			break;
		elif [[ "${status^^}" == "ERROR"* ]]; then
			logMessagePatch "ERROR" "Tenant Paylaod update failed"
			logMessagePatch "INFO" "status : $status"
			logMessagePatch "INFO" "messge : $messge"
			break;
		else
			logMessagePatch "INFO" "Response for Tenant Payload Update, $messge"
		fi
	done
elif [[ "${message}" == *"already a thread performing UPDATE for tenant"* ]];then
	logMessagePatch "ERROR" "Another thread is performing UPDATE for the tenant "
	logMessagePatch "INFO" "status : $status"
	logMessagePatch "INFO" "messge : $messge"
else 
	logMessagePatch "ERROR" "DIS tenant payload update got failed"
	logMessagePatch "INFO" "status : $status"
	logMessagePatch "INFO" "messge : $messge"
fi

}

dis::prepareBip(){
dymmy_bip_Json_file="$1"
for entry in $(cat ./pipeline.env | grep oas_schema_name);  do 
	temp_schema_name=$(echo ${entry} | cut -d '=' -f2 | sed "s/\"//g")
	#kubectl  -n ${SUB_NAMESPACE} get secret schema-details -o jsonpath='{.data.Schemapwd\.json}' | base64 -d > $TENANT_ID/pwd.json
	jq -r 'to_entries | map({(.value.username): .value.password}) | add' "schema_details.json" > "$TENANT_ID/pwd.json"
	temp_schema_password=$(jq -r --arg temp_schema_name $temp_schema_name '.[$temp_schema_name] | select (.!=null)' <<< cat $TENANT_ID/pwd.json)
	if [[ -z $temp_schema_password ]];then
		continue
	fi
	CONN_STRING=$oas_schema_connection_string
	if [[ " ${DFCS_SCHEMAS_SPC[*]} " =~ " ${temp_schema_name} " ]]; then
			CONN_STRING=$DFCS_CONNECTION_STRING
	fi 
	bipjsonFileExists=$(fileContains "jdbcUrl" "$dymmy_bip_Json_file")
	dis::prepareBipJson "$dymmy_bip_Json_file"
done
mv $dymmy_bip_Json_file $TENANT_ID/bip.json
}

# Prepare DV
dis::prepareDV(){
local dymmy_dv_json_file="$1"
for entry in $(cat ./pipeline.env | grep oas_schema_name);  do
	local index=$(echo ${entry} | cut -d '=' -f1)
	local index="${index: -1}"
	temp_schema_name=$(echo ${entry} | cut -d '=' -f2 | sed "s/\"//g")
	temp_datasource_name=$(cat ./pipeline.env | grep oas_datasource_name_$index | cut -d '=' -f2 | sed "s/\"//g")
	if [[ !(" ${DS_DV_NAMES_ARRAY[*]} " =~ " ${temp_datasource_name} ") ]]; then
		continue
	fi		
	#kubectl  -n ${SUB_NAMESPACE} get secret schema-details -o jsonpath='{.data.Schemapwd\.json}' | base64 -d > $TENANT_ID/pwd.json
	jq -r 'to_entries | map({(.value.username): .value.password}) | add' "schema_details.json" > "$TENANT_ID/pwd.json"
	temp_schema_password=$(jq -r --arg temp_schema_name $temp_schema_name '.[$temp_schema_name] | select(.!=null)' <<< cat $TENANT_ID/pwd.json)
	if [[ -z $temp_schema_password ]];then
		continue
	fi

	local CONN_STRING=$oas_schema_connection_string
	if [[ " ${DFCS_SCHEMAS_SPC[*]} " =~ " ${temp_schema_name} " ]]; then
		CONN_STRING=$DFCS_CONNECTION_STRING
		DV_DC_NAME_CLUSTER=${DC_NAME}dfcs
	else
		DV_DC_NAME_CLUSTER=${DC_NAME}fsafnd
	fi 
	local dvJsonFileExists=$(fileContains "technology" "$dymmy_dv_json_file")
	dis::prepareDvJson "$dymmy_dv_json_file"
done
mv $dymmy_dv_json_file $TENANT_ID/dv.json
}


dis::preparePayload(){
	
	oas_schema_connection_string=$(kubectl  -n ${PLATFORM_NAMESPACE} get secret dbaas-details  -o jsonpath='{.data.PdbConnectionString}' | base64 -d)
	DFCS_CONNECTION_STRING=$(cat ./pipeline.env | grep -w FSGBU_DFCS_CDB_CONNECTION_STRING | cut -d '=' -f2 | sed "s/\"//g")
	local DOMAIN=$(echo $DFCS_CONNECTION_STRING |  cut -d "/" -f2 | cut -d "." -f2-)
	local FP=$(echo $DFCS_CONNECTION_STRING |  cut -d "/" -f1)
	local TS_UNDERSCORE=$(sed "s/-/_/g" <<<${TENANT_ID})
	DFCS_CONNECTION_STRING=${FP}/${TS_UNDERSCORE}_svc.${DOMAIN}
	DFCS_SCHEMAS=$(cat ./pipeline.env | grep DFCS_SCHEMAS | cut -d '=' -f2 | sed "s/\"//g")
	DFCS_SCHEMAS_SPC=$(echo ${DFCS_SCHEMAS} | sed "s/,/ /g")
	dummy_conn_json_file="$TENANT_ID/dummy-conpool.json"
	dymmy_bip_Json_file="$TENANT_ID/bipdetails.json"
	dummy_dv_json_file="$TENANT_ID/dvdetails.json"
	
	echo "oas_schema_connection_string : $oas_schema_connection_string"
	echo "DFCS_CONNECTION_STRING : $DFCS_CONNECTION_STRING"
	echo "DOMAIN : $DOMAIN"
	echo "FP : $FP"
	echo "TS_UNDERSCORE : $TS_UNDERSCORE"
	echo "DFCS_CONNECTION_STRING : $DFCS_CONNECTION_STRING"
	echo "DFCS_SCHEMAS : $DFCS_SCHEMAS"
	echo "DFCS_SCHEMAS_SPC : $DFCS_SCHEMAS_SPC"
	
	for entry in $(cat ./pipeline.env | grep oas_schema_name);  do 
	local index=$(echo ${entry} | cut -d '=' -f1)
	local index="${index: -1}"
	temp_schema_name=$(echo ${entry} | cut -d '=' -f2 | sed "s/\"//g")
	echo "temp_schema_name : $temp_schema_name"
	temp_datasource_name=$(cat ./pipeline.env | grep oas_datasource_name_$index | cut -d '=' -f2 | sed "s/\"//g")
	echo "temp_datasource_name : $temp_datasource_name"
	if [[ !(" ${DS_OBIEE_NAMES_ARRAY[*]} " =~ " ${temp_datasource_name} ") ]]; then
		continue
	fi
	jq -r 'to_entries | map({(.value.username): .value.password}) | add' "schema_details.json" > "$TENANT_ID/pwd.json"
	temp_schema_password=$(jq -r --arg temp_schema_name $temp_schema_name '.[$temp_schema_name] | select(.!=null)' <<< cat $TENANT_ID/pwd.json)
	echo "temp_schema_password : $temp_schema_password"
	if [[ -z $temp_schema_password ]];then
		continue    
	fi
	logMessagePatch "INFO" "DIS will be configured for schema : $temp_schema_name - $temp_datasource_name"
	
	# CONN_STRING=$oas_schema_connection_string
	# echo "CONN_STRING : $CONN_STRING"
	if [[ " ${DFCS_SCHEMAS_SPC[*]} " =~ " ${temp_schema_name} " ]]; then
		# CONN_STRING=$DFCS_CONNECTION_STRING	
		# echo "CONN_STRING inside if : $CONN_STRING"
		DC_NAME_CLUSTER=${DC_NAME}dfcs
		echo "DC_NAME_CLUSTER : $DC_NAME_CLUSTER"
	else
		DC_NAME_CLUSTER=${DC_NAME}fsafnd	  
		echo "DC_NAME_CLUSTER else condition : $DC_NAME_CLUSTER"
	fi
	schemaFileExists=$(fileContains "dbUser" "$dummy_conn_json_file")	
	dis::prepareConPoolJson	"$dummy_conn_json_file"
	done
	mv $dummy_conn_json_file $TENANT_ID/conPool.json	
	#prepare-DV
	dis::prepareDV "$dummy_dv_json_file"

}

# Download Wallet for DIS
dis::downloadWallet(){
	
	SAT=$(kubectl -n ${SUB_NAMESPACE} get secrets reserved-service-access-token -o jsonpath='{.data.sat}' | base64 -d)
	WALLET_DATA_FSAFND=$(curl --insecure -X "GET" -H "Authorization:Bearer ${SAT}" -H "Content-Type:application/json" $BASE_SECRET_URLS/$TENANT_ID/wallet-FSGBU-FSAFND | jq -r '.walletdata')
	WALLET_DATA_DFCS=$(curl --insecure -X "GET" -H "Authorization:Bearer ${SAT}" -H "Content-Type:application/json" $BASE_SECRET_URLS/$TENANT_ID/wallet-FSGBU-DFCS | jq -r '.walletdata')
	curl --insecure -X "GET" -H "Authorization:Bearer ${SAT}" -H "Content-Type:application/json" $BASE_SECRET_URLS/$TENANT_ID > schema_details.json
	walletPassword=$(jq -r '."FSGBU-FSAFND" | .password' <<< cat schema_details.json)

}

# Push wallet to shared secret store
dis::pushWalletToSharedSecretStore(){
	
	json_payload=$(mktemp)
cat <<EOF > "$json_payload"
{
  "ttl": 1800,
  "data": {
    "adw_wallet.zip": "${WALLET_DATA_FSAFND}"
  }
}
EOF

	# Use the file as input for curl
	result_s3_fsafnd=$(curl -k -X POST "$SHARED_SS_URL/store/v4/wrapped" \
	-H "Authorization: Bearer $SAT" \
	-H "Content-Type: application/json" \
	--data-binary "@$json_payload")

	rm -rf "$json_payload"


	result_s3_dfcs=$(curl -k -X POST "$SHARED_SS_URL/store/v4/wrapped" -H "Authorization: Bearer $SAT" -H "Content-Type: application/json" -d "{\"ttl\": 1800,\"data\": {\"adw_wallet.zip\": \"${WALLET_DATA_DFCS}\"}}")
	
	echo "INFO" "Shared-Secret-Store response FSAFND- $result_s3_fsafnd"
	echo "INFO" "Shared-Secret-Store response DFCS- $result_s3_dfcs"
	if [[ -z $result_s3_fsafnd || ${result_s3_fsafnd^^} == *"ERROR"* || -z $result_s3_dfcs || ${result_s3_dfcs^^} == *"ERROR"* ]];then
	echo "ERROR" "Wallet could not be pushed to Shared-Secret-Store."
	exit -1
	fi
	WALLET_URI_FSAFND=$(echo -n "$result_s3_fsafnd" | jq -r '.uri | select(.!=null)')
	WALLET_URI_DFCS=$(echo -n "$result_s3_dfcs" | jq -r '.uri | select(.!=null)')
	WALLET_URL_FSAFND=${SHARED_SS_URL}${WALLET_URI_FSAFND}
	WALLET_URL_DFCS=${SHARED_SS_URL}${WALLET_URI_DFCS}
	logMessagePatch "INFO" "Wallet pushed to Shared-Secret-Store Successfully, WALLET_URL_FSAFND=${WALLET_URL_FSAFND},WALLET_URL_DFCS=${WALLET_URL_DFCS}"
	
}

DCDetailsExists(){
	
	oas_access_tok=$(dis::getAccessToken)
	result=$(curl -k -X  "GET" "$DC_URL"  -H "Authorization: Bearer $oas_access_tok" -H "Content-Type: application/json")
	echo >&2 "INFO" "Check If DC exists"
	status_getDetails=$(jq -r '.status' <<< "$result")
	if [[ ${status_getDetails^^} == "FAILED" || $status_getDetails == 500 ]];then
		echo >&2 "INFO" "No direct connect details found. Proceed for fresh.."
		echo "false"
	else
		echo >&2 "INFO" "Direct connect details found. Proceed for update:- $result"
		echo "true"
	fi 
	
}

# Update Direct Connect Schema Json
dis::updateDCSchemasJson(){
local dummy_json_file="$1"
local cluster="$2"
python <<END
import json
import os
schemaEntry={}
search=[]
schemaEntry['dbUser'] = '$temp_schema_name'
schemaEntry['dbPassword'] = '$temp_schema_password'
print(schemaEntry)
with open('$dummy_json_file', 'r') as outfile:
  loaded=json.load(outfile)
for obj in loaded['directConnect']:
  if obj['name'] == '${DC_NAME}$cluster':
    if not obj['schemas']:
      obj['schemas'].append(schemaEntry)
    else:
      for scmaObj in obj['schemas']:
        search.append(scmaObj['dbUser'])
      if '$temp_schema_name' in search:
        continue
      else:
        obj['schemas'].append(schemaEntry)
with open('$dummy_json_file', 'w') as outfile:
  json.dump(loaded, outfile)
END
}

createDCPayload(){ 
logMessagePatch "INFO" "createDCPayload"
DC_NAME=adw$(echo "${TENANT_ID}" | sed 's/-//')

if [[ $(echo -n  "${DISTokenURL: -1}") == "/" ]];then
tok_url=$(echo ${DISTokenURL::-1})
else
tok_url=$DISTokenURL
fi

logMessagePatch "INFO" "tok_url:$tok_url"
DC_URL="${tok_url}/platform/credentials/v1/tenant/${DIS_TENANT_ID}/directConnect"
logMessagePatch "INFO" "DC_URL:$DC_URL"
DFCS_SCHEMAS=$(cat ./pipeline.env | grep DFCS_SCHEMAS | cut -d '=' -f2 | sed "s/\"//g")
DFCS_SCHEMAS_SPC=$(echo ${DFCS_SCHEMAS} | sed "s/,/ /g")

dummy_direct_connect_file="$TENANT_ID/dummy-direct-connect.json"  

#DC_PAYLOAD_TEMPLATE="{\"directConnect\": [{\"name\": \"${DC_NAME}fsafnd\",\"disPlatformVersion\": \"22.12.1\",\"walletUrl\": \"$WALLET_URL_FSAFND\",\"defaultServiceName\": \"high\",\"walletPassword\" : \"$walletPassword\",\"databaseType\": \"adw\",\"schemas\": []},{\"name\": \"${DC_NAME}dfcs\",\"disPlatformVersion\": \"22.12.1\",\"walletUrl\": \"$WALLET_URL_DFCS\",\"defaultServiceName\": \"high\",\"walletPassword\" : \"$walletPassword\",\"databaseType\": \"adw\",\"schemas\": []}]}"
DC_PAYLOAD_TEMPLATE="{\"directConnect\": [{\"name\": \"${dc_fsafnd}\",\"walletUrl\": \"$WALLET_URL_FSAFND\",\"defaultServiceName\": \"high\",\"walletPassword\" : \"$walletPassword\",\"databaseType\": \"adw\",\"schemas\": []}]}"

echo "$DC_PAYLOAD_TEMPLATE" > $dummy_direct_connect_file

count=0
for entry in $(cat ./pipeline.env | grep oas_schema_name);  do
count=$((count+1))
local index=$(echo ${entry} | cut -d '=' -f1)
local index="${index: -1}"
temp_schema_name=$(echo ${entry} | cut -d '=' -f2 | sed "s/\"//g")
temp_datasource_name=$(cat ./pipeline.env | grep oas_datasource_name_$index | cut -d '=' -f2 | sed "s/\"//g")
if [[ !(" ${DS_OBIEE_NAMES_ARRAY[*]} " =~ " ${temp_datasource_name} " || " ${DS_DV_NAMES_ARRAY[*]} " =~ " ${temp_datasource_name} ") ]]; then
	logMessagePatch "WARN" "$temp_datasource_name not present in $DS_DV_NAMES_ARRAY or $DS_OBIEE_NAMES_ARRAY continue"
	continue
fi
schema=${temp_schema_name^^}
temp_schema_password=$(jq -r --arg schema $schema '.[$schema] | .password | select(.!=null)' <<< cat schema_details.json)
local cluster=$(jq -r --arg schema $schema '. | .[$schema] | .cluster' <<< cat schema_details.json)
if [[ -z $temp_schema_password ]];then
	logMessagePatch "WARN" "Password does not exists in admin json for schema=$schema continue.."
	continue
fi
if [[ $cluster == "FSGBU-DFCS" ]];then
	dis::updateDCSchemasJson "$dummy_direct_connect_file" "dfcs"
else
	dis::updateDCSchemasJson "$dummy_direct_connect_file" "fsafnd"
fi

done
mv $dummy_direct_connect_file $TENANT_ID/direct_connect_payload.json
cat $TENANT_ID/direct_connect_payload.json | jq .
}

updateDCDetails(){
#Get the direct connect details if exists for tenant.
logMessagePatch "INFO" "updateDCDetails-------->"
createDCPayload 
# DCDet_Update=$(jq 'del(.directConnect[] | .walletUrl,.walletPassword)' <<< cat $TENANT_ID/direct_connect_payload.json)
# echo -n "$DCDet_Update" > $TENANT_ID/direct_connect_payload.json
cat $TENANT_ID/direct_connect_payload.json | jq .
}

# Push Direct Connect Details to Secret Store
dis::pushDCDetlToSharedSS(){  
	
	logMessagePatch "INFO" "pushDCDetlToSharedSS"
	if [[ $(echo -n  "${DISTokenURL: -1}") == "/" ]];then
	tok_url=$(echo ${DISTokenURL::-1})
	else
	tok_url=${DISTokenURL}
	fi
	DC_URL="${tok_url}/platform/credentials/v1/tenant/${DIS_TENANT_ID}/directConnect"
	result=$(DCDetailsExists) 
	echo "result=$result"
	if [[ $result == "true" ]];then
		updateDCDetails
		DC_ACTION="PUT"
	else
		createDCPayload
		DC_ACTION="POST"
	fi  
	
	oas_access_tok=$(dis::getAccessToken)
	logMessagePatch "INFO" "DCURL: $DC_URL"
	
	result_dirConnect=$(curl -k -X  "$DC_ACTION" "$DC_URL"  -H "Authorization: Bearer $oas_access_tok" -H "Content-Type: application/json" -d @$TENANT_ID/direct_connect_payload.json)
	logMessagePatch "INFO" "result_dirConnect: $result_dirConnect"
	status=$(jq -r '.status' <<< "$result_dirConnect")	
	message=$(jq -r '.message' <<< "$result_dirConnect")	
	logMessagePatch "INFO" "result_dirConnect-status=$status"
	
	if [[ ! -z $result_dirConnect && ${status^^} == "SUCCESS" ]];then
		if [[ ${message^^} == *"SAVED SUCCESSFULLY"* ]];then
			logMessagePatch "INFO" "Direct connect details saved successfully."
		else
			logMessagePatch "INFO" "Direct connect Details submitted Successfully. Fetching update status..."
			checkDCSaveStatus 
		fi
		
	else
		logMessagePatch "ERROR" "Direct connect details push failed - $result_dirConnect"
		exit -1
	fi
}

dis::onboardTenant(){
	local artifact_version="$1"
	#preparePayload
	dis::preparePayload	
	
	local schema_details_json=$(cat $TENANT_ID/conPool.json)
	local dvJson=$(cat $TENANT_ID/dv.json)
	
	#constructPayload
	dis::constructPayload "$artifact_version"
	local dis_tenant_payload=$TENANT_ID/dis_tenant_payload.json
	
	if [ ${ONBOARD_DIS_TENANT^^} == "TRUE" ];then
		echo "PAYLOAD is--"
		cat $dis_tenant_payload | jq .	
		result=$(dis::onboardTenantAndStatus "$onboardApiAction" "$dis_tenant_payload") 
		#checkstatus
		checkstatus 
	fi

}

dis::generatePartialTenantPayload() {

python <<END
import json
data = {}
visualization = {}
dv = {}
deploymentTemplate=['visualization']
oasObj = {}
oas = []

# Load JSON input from shell variables
connectionPool = json.loads("""$schema_details_json""")
dvConnectionsArr = json.loads("""$dvJson""")
data['deploymentTemplate'] = deploymentTemplate
oasObj["connectionPool"] = connectionPool
oasObj['isBIPEnabled'] = True
dv["connections"] = dvConnectionsArr
oasObj["dv"] = dv
oas.append(oasObj)

visualization["oas"] = oas
data["visualization"] = visualization

# Write output payload
with open("$TENANT_ID/dis_tenant_update_payload.json", "w") as outfile:
    json.dump(data, outfile, indent=2, sort_keys=True)
END

  logMessagePatch "INFO" "Updated PAYLOAD for GBU_ID-$TENANT_ID, DIS_TENANT_ID-$DIS_TENANT_ID : "
  cat "$TENANT_ID/dis_tenant_update_payload.json" | jq .
  
}

dis::udpateDISTenantPayload(){
#preparePayload
dis::preparePayload	
#constructPayload
local schema_details_json=$(cat $TENANT_ID/conPool.json)	
#bipJson=$(cat $TENANT_ID/bip.json)
local dvJson=$(cat $TENANT_ID/dv.json)
dis::generatePartialTenantPayload

local dis_tenant_payload=$TENANT_ID/dis_tenant_update_payload.json
echo "DIS Tenant Payload to be updated: "
cat $dis_tenant_payload | jq .	

result=$(dis::updateDISTenantPartialPayload "PUT" "$DIS_TENANT_ID" "$dis_tenant_payload") 
checkstatus 

}


dis::updatePatchDetailsSecret(){
echo "Update Dis Secret in patch-details.."	
local patch_det_exist=$(kubectl -n ${SUB_NAMESPACE} get secret patch-details -o jsonpath='{.data}' --ignore-not-found=true 2>/dev/null)

local idcsClientId=$(jq -r '.applicationId | select (.!=null)' <<< cat $TENANT_ID/dis_tenant_payload.json)
local applicationId=$(jq -r '.idcsClientId | select (.!=null)' <<< cat $TENANT_ID/dis_tenant_payload.json)
local idcsClientSecret=$(jq -r '.idcsClientSecret | select (.!=null)' <<< cat $TENANT_ID/dis_tenant_payload.json)

local DIS_ONBOARDING_PAYLOAD_ENC=$(cat $TENANT_ID/dis_tenant_payload.json | base64 -w 0)
local PATCH_VERSION_ENC=$(echo -n $PATCH_VERSION | base64 -w 0)
local DIS_ARTIFACT_VERSION_ENC=$(echo -n $DIS_ARTIFACT_VERSION | base64 -w 0)
local idcsClientId_enc=$(echo -n $idcsClientId | base64 -w 0)
local applicationId_enc=$(echo -n $applicationId | base64 -w 0)
local idcsClientSecret_enc=$(echo -n $idcsClientSecret | base64 -w 0)
local DIS_ONBOARDING_DONE_ENC=$(echo -n ${ONBOARD_DIS_TENANT^^} | base64 -w 0)

if [[ ${ONBOARD_DIS_TENANT^^} == "TRUE" ]];then
	if [[ -z $patch_det_exist ]];then
		kubectl -n ${SUB_NAMESPACE} create secret generic patch-details \
				--from-literal patchVersion="${PATCH_VERSION}" \
				--from-literal DIS_UPGRADED_VERSION="${DIS_ARTIFACT_VERSION}" \
				--from-literal IS_DIS_ONBOARDING_DONE="${ONBOARD_DIS_TENANT^^}" \
				--from-literal DIS_APP_IDCS_CLIENT_ID="${idcsClientId}" \
				--from-literal DIS_APP_APPLICATION_ID="${applicationId}" \
				--from-literal DIS_APP_IDCS_CLIENT_SECRET="${idcsClientSecret}" \
				--from-file DIS_ONBOARDING_PAYLOAD="$TENANT_ID/dis_tenant_payload.json" \
				--dry-run=client -o yaml | kubectl apply -f -
	else
		kubectl -n ${SUB_NAMESPACE} get secret patch-details -o json \
		| jq --arg PATCH_VERSION_ENC $PATCH_VERSION_ENC '.data["patchVersion"] |= $PATCH_VERSION_ENC' \
		| jq --arg DIS_UPGRADED_VERSION_ENC $DIS_ARTIFACT_VERSION_ENC '.data["DIS_UPGRADED_VERSION"] |= $DIS_UPGRADED_VERSION_ENC' \
		| jq --arg DIS_ONBOARDING_DONE_ENC $DIS_ONBOARDING_DONE_ENC '.data["IS_DIS_ONBOARDING_DONE"] |= $DIS_ONBOARDING_DONE_ENC' \
		| jq --arg DIS_APP_IDCS_CLIENT_ID_ENC $idcsClientId_enc '.data["DIS_APP_IDCS_CLIENT_ID"] |= $DIS_APP_IDCS_CLIENT_ID_ENC' \
		| jq --arg DIS_APP_APPLICATION_ID_ENC $applicationId_enc '.data["DIS_APP_APPLICATION_ID"] |= $DIS_APP_APPLICATION_ID_ENC' \
		| jq --arg DIS_APP_IDCS_CLIENT_SECRET_ENC $idcsClientSecret_enc '.data["DIS_APP_IDCS_CLIENT_SECRET"] |= $DIS_APP_IDCS_CLIENT_SECRET_ENC' \
		| jq --arg DIS_ONBOARDING_PAYLOAD_ENC "$DIS_ONBOARDING_PAYLOAD_ENC" '.data["DIS_ONBOARDING_PAYLOAD"] |= $DIS_ONBOARDING_PAYLOAD_ENC' | kubectl apply -f -
	fi
else
	if [[ -z $patch_det_exist ]];then		
		kubectl -n ${SUB_NAMESPACE} create secret generic patch-details \
				--from-literal patchVersion="${PATCH_VERSION}" \
				--from-literal DIS_UPGRADED_VERSION="${DIS_ARTIFACT_VERSION}" \
				--from-literal IS_DIS_ONBOARDING_DONE="${ONBOARD_DIS_TENANT^^}" \
				--from-literal DIS_APP_IDCS_CLIENT_ID="${idcsClientId}" \
				--from-literal DIS_APP_APPLICATION_ID="${applicationId}" \
				--from-literal DIS_APP_IDCS_CLIENT_SECRET="${idcsClientSecret}" \
				--dry-run=client -o yaml | kubectl apply -f -
	else
		kubectl -n ${SUB_NAMESPACE} get secret patch-details -o json \
		| jq --arg PATCH_VERSION_ENC $PATCH_VERSION_ENC '.data["patchVersion"] |= $PATCH_VERSION_ENC' \
		| jq --arg DIS_UPGRADED_VERSION_ENC $DIS_ARTIFACT_VERSION_ENC '.data["DIS_UPGRADED_VERSION"] |= $DIS_UPGRADED_VERSION_ENC' \
		| jq --arg DIS_ONBOARDING_DONE_ENC $DIS_ONBOARDING_DONE_ENC '.data["IS_DIS_ONBOARDING_DONE"] |= $DIS_ONBOARDING_DONE_ENC' \
		| jq --arg DIS_APP_IDCS_CLIENT_ID_ENC $idcsClientId_enc '.data["DIS_APP_IDCS_CLIENT_ID"] |= $DIS_APP_IDCS_CLIENT_ID_ENC' \
		| jq --arg DIS_APP_APPLICATION_ID_ENC $applicationId_enc '.data["DIS_APP_APPLICATION_ID"] |= $DIS_APP_APPLICATION_ID_ENC' \
		| jq --arg DIS_APP_IDCS_CLIENT_SECRET_ENC $idcsClientSecret_enc '.data["DIS_APP_IDCS_CLIENT_SECRET"] |= $DIS_APP_IDCS_CLIENT_SECRET_ENC' | kubectl apply -f -
	fi
	logMessagePatch "INFO" "patch-details secret updated syccessfully"
 fi

}

dis::disTenantExists() {

	oas_access_tok=$(dis::getAccessToken)
	DIS_TENANT_ID=$(kubectl -n ${SUB_NAMESPACE} get secrets oasdisurls -o jsonpath='{.data.oasurls}' --ignore-not-found=true 2> /dev/null | base64 -d | jq '.oas[0].analyticsUrl' | grep -o '[^/]*' | tr '\n' ' ' | grep -o '[^/]*$' |  cut -d " " -f3 | sed 's/ *$//g')
	if [[ -z $DIS_TENANT_ID ]];then
		DIS_TENANT_ID=$(dis::getDisTidByGbuTid "$oas_access_tok" "$TENANT_ID")
	fi
	echo "$DIS_TENANT_ID"
	
}


dis::upgrade_dis_tenant() {

	DS_OBIEE_NAMES_ARRAY=$(echo $DS_OBIEE_NAMES | sed "s/,/ /g")
	DS_DV_NAMES_ARRAY=$(echo $DS_DV_NAMES | sed "s/,/ /g")	
	
	#checking updagrade_dis flag
	if [[ $UPGRADE_DIS != "" && $UPGRADE_DIS == "false" ]];then
		logMessagePatch "INFO" "DIS Upgrade is not enabled for this patch. Skipping the stage"
		exit 0
	fi
    
	#Gettting the dis_tenant_id for existing provisioned tenant.
    DIS_TENANT_ID=$(dis::disTenantExists)
    if [[ -z $DIS_TENANT_ID ]];then
		logMessagePatch "ERROR" "DIS is not configured for the dfcs-tenant. Exiting Setup"
		exit 1
	fi

    logMessagePatch "INFO" "DIS_TENANT_ID=$DIS_TENANT_ID"
	if [[ $CI_DIS_ARTIFACT_VERSION != "NA" ]];then
		DIS_ARTIFACT_VERSION=$CI_DIS_ARTIFACT_VERSION
		logMessagePatch "INFO" "CI Provided DIS_ARTIFACT_VERSION=$CI_DIS_ARTIFACT_VERSION will be used."
	fi
	logMessagePatch "INFO" "DIS_ARTIFACT_VERSION=$DIS_ARTIFACT_VERSION"
	
	
	tenantJson=$(dis::getTenantObjDetFrmList "$oas_access_tok" "$DIS_TENANT_ID")
	if [[ -z "$tenantJson" ]];then
		logMessagePatch "ERROR" "No DIS tenant Registered with dfcs-tenant. Exiting setup.."
		exit 0
    fi
	
	registeredVersion=$(jq -r '.version' <<< "$tenantJson")
	if [[ $registeredVersion == "multiProduct" ]];then
		lastRegisteredVersion=$(jq -r '.products[] | select (.!=null) | select(.name=="fsafnd") | .version' <<< "$tenantJson")
	else
		lastRegisteredVersion=$registeredVersion
	fi
	logMessagePatch "INFO"  "lastRegisteredVersion=$lastRegisteredVersion, DIS_TENANT_ID=$DIS_TENANT_ID, DIS_FORCE_DELETE=$DIS_FORCE_DELETE"

    if [[ $(versionCheck $lastRegisteredVersion ge "$DIS_ARTIFACT_VERSION") == "true" && ${DIS_FORCE_DELETE^^} == "TRUE" ]];then
	logMessagePatch "WARN"  "Proceed Degrade from $lastRegisteredVersion to $DIS_ARTIFACT_VERSION, Dis tenant Soft delete will be done."
    fi

	if [[ (! -z $lastRegisteredVersion) && ($lastRegisteredVersion != "0.0.0") && ${DIS_FORCE_DELETE^^} == "TRUE" ]];then
		#soft delete
		logMessagePatch "INFO"  "Proceed to soft delete DIS_TENANT_ID=$DIS_TENANT_ID"
		deregisterFlag="false"
		result=$(dis::deleteOASTenantAndStatus "$oas_access_tok" "DELETE" "$DIS_TENANT_ID" "$deregisterFlag")
		checkDeleteStatus "$result" "$deregisterFlag"
		lastRegisteredVersion="0.0.0"
	fi
    
	UPDATE_FLAG=false
	#When tenant created but not onboarded or distenant soft deleted.
	if [[ "$lastRegisteredVersion" == "0.0.0" ]];then
		onboardApiAction="POST"	
		ONBOARD_DIS_TENANT=true
		logMessagePatch "INFO" "Onboard the Tenant to dis_artifact_version=$DIS_ARTIFACT_VERSION"
		dis::downloadWallet
		logMessagePatch "INFO" "Wallet downloaded.."
		dis::pushWalletToSharedSecretStore
		logMessagePatch "INFO" "Wallet pushed to shared secret store.."
		dis::pushDCDetlToSharedSS
		logMessagePatch "INFO" "Direct Connect payload pushed to shared secret store.."
		dis::onboardTenant "$DIS_ARTIFACT_VERSION"
		lastRegisteredVersion="$DIS_ARTIFACT_VERSION"
		UPDATE_FLAG=true
    
		# Enabling OAC access to OAS
		logMessagePatch "INFO" "DR				= $DR"
		logMessagePatch "INFO" "APP_NAME_UPPER	= $APP_NAME_UPPER"
		logMessagePatch "INFO" "DIS_TENANT_ID	= $DIS_TENANT_ID"
		logMessagePatch "INFO" "SUB_NAMESPACE	= $SUB_NAMESPACE"
		logMessagePatch "INFO" "Enabling OAC access started"
		dis::enableOAC "$DR" "$APP_NAME_UPPER" "$DIS_TENANT_ID" "$SUB_NAMESPACE" 
		logMessagePatch "INFO" "Enabling OAC access to OAS completed"

    else		
		if [[ $registeredVersion != "multiProduct" ]];then
			logMessagePatch "INFO" "DIS_TENANT_ID=$TENANT_ID is not a multiProduct Tenant, Only Multi-product and shared tenant upgrade is supported"
			exit -1
		fi
		
		logMessagePatch "INFO" "Download wallet"
		dis::downloadWallet
		logMessagePatch "INFO" "Push wallet to S3"
		dis::pushWalletToSharedSecretStore
		logMessagePatch "INFO" "DIS_TENANT_ID=$DIS_TENANT_ID"
		logMessagePatch "INFO" " checking Direct connect existance "
		dis_token=$(dis::getAccessToken)
		#echo $dis_token
		logMessagePatch "INFO" " token url: $DISTokenURL "
		
		result=$(curl -k -s -X GET \
		"${DISTokenURL}/platform/credentials/v1/tenant/${DIS_TENANT_ID}/directConnect" \
		-H "Authorization: Bearer $dis_token" \
		-H "Content-Type: application/json")
		
		status_getDetails=$(jq -r '.status // empty' <<< "$result")
		
		if [[ ${status_getDetails^^} == "FAILED" || $status_getDetails == "500" ]]; then
			logMessagePatch "INFO" "Direct Connect Details not found : $result "
			dc_fsafnd="adw$(echo "${TENANT_ID}" | sed 's/-//')fsafnd"
			dc_dfcs="adw$(echo "${TENANT_ID}" | sed 's/-//')dfcs"
		else
			logMessagePatch "INFO" "Direct Connect Details found : $result "
		
			dc_fsafnd=$(echo "$result" | jq -r '.directConnect[].name' | grep fsafnd || true)
			dc_dfcs=$(echo "$result" | jq -r '.directConnect[].name' | grep dfcs   || true)
		
			# fallback if grep found nothing
			if [[ -z "$dc_fsafnd" ]]; then
				dc_fsafnd="adw$(echo "${TENANT_ID}" | sed 's/-//')fsafnd"
				logMessagePatch "WARN" "No fsafnd direct connect found in API, using fallback: $dc_fsafnd"
			fi
			if [[ -z "$dc_dfcs" ]]; then
				dc_dfcs="adw$(echo "${TENANT_ID}" | sed 's/-//')dfcs"
				logMessagePatch "WARN" "No dfcs direct connect found in API, using fallback: $dc_dfcs"
			fi
		fi
		
		echo "dc_fsafnd: $dc_fsafnd"
		echo "dc_dfcs: $dc_dfcs"
		
		dc_fsafnd_count=$(echo "${dc_fsafnd^^}" | grep -o "FSAFND" | wc -l)
		dc_dfcs_count=$(echo "${dc_dfcs^^}"   | grep -o "DFCS"   | wc -l)
		
		if [[ $dc_fsafnd_count -gt 1 || $dc_dfcs_count -gt 1 ]]; then
			logMessagePatch "ERROR" "Multiple direct connect details found for DB"
			exit 1
		fi

		# push DC details 
		dis::pushDCDetlToSharedSS
		logMessagePatch "INFO" "DIS Tenant Payload update has been initiated"

        dis::udpateDISTenantPayload
	    logMessagePatch "INFO" "DIS Tenant Payload update has been completed"
		
		onboardApiAction="PUT"
		logMessagePatch "INFO" "Proceed Upgrade from $lastRegisteredVersion to $DIS_ARTIFACT_VERSION"
		logMessagePatch "INFO" "Reading DIS Platform version from configmaps"

	DIS_Platform_Version=$(kubectl -n ${COMMON_NAMESPACE} get configmap oas-dis-config -o json | jq .data.displatformversion -r)

	if [[ ${DIS_Platform_Version} ]];then
		logMessagePatch "INFO" "DIS_Platform_Version :$DIS_Platform_Version"
		dis::upgradeTenantVersion "$DIS_ARTIFACT_VERSION"
	else 
		logMessagePatch "ERROR" "DIS_Platform_Version not configured in configmaps "
		exit 1
	fi
		
		# Enabling OAC access to OAS
		logMessagePatch "INFO" "DR				= $DR"
		logMessagePatch "INFO" "APP_NAME_UPPER	= $APP_NAME_UPPER"
		logMessagePatch "INFO" "DIS_TENANT_ID	= $DIS_TENANT_ID"
		logMessagePatch "INFO" "SUB_NAMESPACE	= $SUB_NAMESPACE"
		logMessagePatch "INFO" "Enabling OAC access started"
		dis::enableOAC "$DR" "$APP_NAME_UPPER" "$DIS_TENANT_ID" "$SUB_NAMESPACE" 
		logMessagePatch "INFO" "Enabling OAC access to OAS completed"

		# Enabling BIP
		# logMessagePatch "INFO" "Enabling BIP started..."
		# BIP_result=$(dis::enableBIP "PUT" "$DIS_TENANT_ID")
		# logMessagePatch "INFO" "BIP_result : $BIP_result"
		# checkPayloadUpdateStatus "$BIP_result"
		# logMessagePatch "INFO" "Enabling BIP completed"

fi
       if [[ $UPDATE_FLAG == true ]];then
	      dis::updatePatchDetailsSecret
       fi
       logMessagePatch "INFO" "Execution Step Sucessful.."
}

#Start Execution
patch::initialize "fsgbu-fsafnd"
dis::upgrade_dis_tenant
