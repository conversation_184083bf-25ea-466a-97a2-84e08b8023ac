#!/bin/bash
set -e
patch::replace_placeholders() {	
sed -i \
		-e "s|__BUCKET_PREFIX__|${oci_os_bucket_name_prefix}|g" \
		-e "s|__ICAP_URL__|${ICAP_URL}|g" \
		-e "s|__TENANT_ID__|${TENANT_ID}|g" \
		-e "s|__TENANT_STRING__|${TENANT_STRING}|g" \
		-e "s|__COMMON_SUBNAMESPACE__|${SUB_NAMESPACE}|g" \
		-e "s|__BUCKET_NAME__|${oci_os_bucket_name_prefix}${TENANT_ID}|g" \
		-e "s|__REGION__|${oci_region}|g" \
		-e "s|__BUCKETNAME__|${oci_os_bucket_name_prefix}${TENANT_ID}|g" \
		-e "s|__DIS_ARTIFACT_VERSION__|${DIS_ARTIFACT_VERSION}|g" \
		-e "s|{{PREFIX}}|${PREFIX}|g" \
		-e "s|{{REGISTRY}}|${REGISTRY}|g" \
		-e "s|{{ARTPROJECT}}|${ARTPROJECT}|g" \
		-e "s|{{IS_ADW}}|${IS_ADW}|g" \
		-e "s|__SMTP_PROXY_HOST__|${smtp_proxy_host}|g" \
		-e "s|__SS_URL__|${BASE_SECRET_URLS}$TENANT_ID|g" \
		-e "s|__BASE_SECRET_URLS__|${BASE_SECRET_URLS}|g" \
		-e "s|__COMMON_NAMESPACE__|${COMMON_NAMESPACE}|g" \
		-e "s|__MQSSERVICENAME__|${serviceid}|g" \
		-e "s|__MAINTENANCE_FLAG__|${IS_MAINTENANCE_ENABLED}|g" \
		-e "s|__APPID__|${appid}|g" \
		-e "s|__COMPARTMENT_ID__|${oci_cmpt_ocid}|g" \
		-e "s|__SERVICE_ENTITLEMENT_ID__|${SERVICE_ENTITLEMENT_ID_RET}|g" \
		-e "s|__IS_ADW__|$IS_ADW|g" \
		$1
if [[ "$2" == "true" ]];then
	sed -i \
			-e "s|__CURRENT_CLUSTER__|${COMMON_NAMESPACE^^}|g" \
			$1
else
	sed -i \
			-e "s|__CURRENT_CLUSTER__|${COMMON_NAMESPACE}|g" \
			$1
fi

}

replace_app_image_tag_versions(){
dirctry=${1:-./}
file="${2:-temp.props}"
patterns=""
set +e
cp ${dirctry}fsafnd_image_versions.props $file
echo -e -n "\n" >> temp.props
cat ${dirctry}dfcs_image_versions.props >> $file
sed -i '/^$/d' $file

if [ -f "$file" ];then
echo -e "\n" >> "$file" 
while read -r line; do 
	name=$(echo $line | cut -d '=' -f1)
	tag=$(echo $line | cut -d '=' -f2)
	
	if [ ! -z ${name} ];then
		if [ ${name} == "dis-artifacts" ];then
			export DIS_ARTIFACT_VERSION=$tag
		elif [[ "${name}" == *"-artifacts"* ]];then
			patterns="${patterns} s/${name}:__TAG__/${tag}/g;"
		else
			if [[ "${line}" == *"-av="* ]];then
				if [[ ${patterns} != *"${name}:__TAG__"* ]];then
					patterns="${patterns} s/${name}:__TAG__/${tag}/g;"
				fi
			else
				if ! grep -q "${name}-av" "$file"; then
					patterns="${patterns} s/${name}:__TAG__/${name}:${tag}/g; s/${name}-av:__TAG__/${tag}/g;"
				else
					if [[ ${patterns} != *"${name}-av:__TAG__"* ]];then
						av_val=$(grep "${name}-av=" "$file")
						av_tag=$(echo $av_val | cut -d '=' -f2)
						patterns="${patterns} s/${name}-av:__TAG__/${av_tag}/g;"
					fi
					if [[ ${name} != *"-av" && ${patterns} != *"${name}:__TAG__"* ]];then
						patterns="${patterns} s/${name}:__TAG__/${name}:${tag}/g;"
					fi

				fi
			fi
			
		fi
	fi
done < "$file"
if [ -d "$dirctry" ];then
	find "$dirctry" -type f \( -iname \*.yaml \) | while read fname; do sed -i "${patterns}" $fname; done;
fi
else
	echo "$file file does not exist. Exiting..."
	exit 1
fi
rm -rf $file
set -e
}

patch::initialize(){	
    export CURRENT_CLUSTER=$(echo "$SUB_NAMESPACE" | sed 's/--.*//g')	
	mkdir -p wallet 
	
	./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -rf $deployments_artifacts_path/fsafnd_image_versions.props ./ -e k8s	
	./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -rf $deployments_artifacts_path/dfcs_image_versions.props ./ -e k8s	

	./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -rf $deployments_artifacts_path/payload.json ./ -e k8s
	./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -rf $deployments_artifacts_path/env/$MSP_PROJ_NAME/pipeline-$OST_ENVIRONMENT.env ./env/$MSP_PROJ_NAME/ -e k8s	
	chmod 755 ./parse_json_payload.py
	./parse_json_payload.py "$PATCH_PAYLOAD" "$PATCH_HOME" "$OST_ENVIRONMENT" "$OST_CLASS" "$TENANT_ID"
	sed -i \
			-e "s|__PATCH_ID__|${PATCH_ID}|g" \
			$PATCH_HOME/patch_pipeline_services.env				
	
	set -a
	source ./patch_pipeline_services.env
	mv ./env/$MSP_PROJ_NAME/pipeline-$OST_ENVIRONMENT.env pipeline.env
	source ./pipeline.env	
	set +a
	
	replace_app_image_tag_versions
	IS_ADW_RET=$(kubectl -n $SUB_NAMESPACE get secrets configuration.properties -ojson --ignore-not-found=true 2>/dev/null | jq -r '.data."configuration.properties"'  | base64 -d | grep IS_ADW | cut -d '=' -f2)
	SERVICE_ENTITLEMENT_ID_RET=$(kubectl -n $SUB_NAMESPACE get secrets configuration.properties -ojson --ignore-not-found=true 2>/dev/null | jq -r '.data."configuration.properties"'  | base64 -d | grep SERVICE_ENTITLEMENT_ID | cut -d '=' -f2)
	IS_MAINTENANCE_ENABLED=$(kubectl -n $SUB_NAMESPACE get secrets configuration.properties -ojson --ignore-not-found=true 2>/dev/null | jq -r '.data."configuration.properties"'  | base64 -d | grep IS_MAINTENANCE_ENABLED | cut -d '=' -f2)
	IS_MAINTENANCE_ENABLED=${IS_MAINTENANCE_ENABLED,,}
	if [[ -z $IS_MAINTENANCE_ENABLED ]];then
		IS_MAINTENANCE_ENABLED="false"
	fi
	IS_ADW_RET=${IS_ADW_RET,,}
	if [[ -z $IS_ADW_RET ]];then
		IS_ADW_RET="false"
	fi
	export IS_ADW=$IS_ADW_RET
	echo "IS_ADW Enabled=$IS_ADW"
	echo "All Env files Loaded..."
}

#####DIS Functions#####
dis::getAccessToken(){
  #get Access Token
  #logMessage "INFO" "Fetching OAS Access Token"
    local result=`curl -s -X POST "$DISTokenURL/oauth/token?grant_type=client_credentials" \
            -H "Authorization:Basic $(echo -n "$PartnerclientID:$PartnerSecret" | base64 -w 0)" \
            -H "Content-Type: application/json"`
	local oas_access_tok=$(jq -r '.access_token' <<< "$result")	
    if [[ -z $oas_access_tok ]];then
		echo >&2 "Error communicating to DIS_ACCESS_TOKEN Server, Existing set up.."
		exit -1
	fi
    echo "$oas_access_tok"  
}
dis::onboardTenantAndStatus(){
	local actionMethod="$1"
	local dis_tenant_payload="$2"
	oas_access_tok=$(dis::getAccessToken)
	local result=`curl -s --location --request $actionMethod ''$DISTokenURL'/onboarding/v2/partner/'$PartnerName'/tenant/'$DIS_TENANT_ID'' \
	--header 'accept: application/json' \
	--header 'Content-Type: application/json' \
	--header 'Authorization: Bearer '$oas_access_tok'' \
	--data @${dis_tenant_payload}`	
  echo "$result"
}

dis::upgradeDisTenant(){
	local access_token="$1"
	local actionMethod="$2"
	local tenantsCommaSep="$3"
	local upgrade_dis_json="$4"

	local result=`curl -s -X $actionMethod \
	"$DISTokenURL/onboarding/v2/partner/$PartnerclientID/upgrade" \
	-H "Authorization: Bearer $access_token" \
	-H "Content-Type: application/json" \
	-d "$upgrade_dis_json"`
  echo "$result"
}

dis::getUpgradeTenantStatus(){
   local result=`curl -k -s -X GET \
  "$DISTokenURL/onboarding/v2/partner/$PartnerclientID/upgrade/requestId/$2" \
  -H "Authorization: Bearer $1"`  
  echo "$result"
}

dis::getDISTenantDetails(){
	DIS_TENANT_ID=$2
	oas_access_tok=$1
	local result=`curl -k -s -X GET \
	"$DISTokenURL/onboarding/v2/partner/$PartnerclientID/tenant/$DIS_TENANT_ID" \
	-H "Authorization: Bearer $oas_access_tok"`  
	echo "$result"
}

dis::getDISPlatformVersions(){
	oas_access_tok=$1
	local result=`curl -k -s -X GET \
	"$DISTokenURL/onboarding/v2/platform/versions" \
	-H "Authorization: Bearer $oas_access_tok"`  
	echo "$result"
}

dis::getOASTenants(){
   local OASTenants=`curl -k -s -X GET \
  "$DISTokenURL/onboarding/v2/partner/$PartnerclientID"/tenants \
  -H "Authorization: Bearer $1"`  
  echo "$OASTenants"
}
dis::getTenantObjDetFrmList(){
	
	local oas_access_tok="$1"
	if [[ -z $oas_access_tok ]];then
		oas_access_tok=$(dis::getAccessToken)
	fi	
	local DIS_TENANT_ID="$2"
	local tList=$(dis::getOASTenants "$oas_access_tok")
	local tObj=$(jq -r --arg DIS_TENANT_ID $DIS_TENANT_ID '.[] | select(.tenantId==$DIS_TENANT_ID)' <<< "$tList")
	echo "$tObj"
	
}
dis::getDisTidByGbuTid(){
	
	local oas_access_tok="$1"
	if [[ -z $oas_access_tok ]];then
		oas_access_tok=$(dis::getAccessToken)
	fi	
	gbu_tenant_id="$2"
	local tList=$(dis::getOASTenants "$oas_access_tok")
	local tid=$(jq -r --arg gbu_tenant_id $gbu_tenant_id '.[] | select(.tenantName==$gbu_tenant_id) | .tenantId' <<< "$tList")
	echo $tid | sed 's/ *$//g'
	
}
dis::deleteOASTenantAndStatus(){
	local oas_access_tok="$1"
	local ACTION="$2"
	local DIS_TENANT_ID="$3"
	local deregisterFlag="$4"
	deregister=""
	if [[ ! -z $deregisterFlag && ${deregisterFlag^^} == "FALSE" ]];then
		deregister="?deregister=false"
	fi
	url=${DISTokenURL}/onboarding/v2/partner/${PartnerclientID}/tenant/${DIS_TENANT_ID}$deregister
	#echo >&2 "deregisterFlag=$deregisterFlag,ACTION=$ACTION,Delete url=$url"
	result=`curl -k -s -X $ACTION \
			"$url" \
			-H "Authorization: Bearer $oas_access_tok"`  
	echo "$result"
}

dis::createNewDISTenant(){    
	loca oas_access_tok="$1"
	if [[ -z $oas_access_tok ]];then
		oas_access_tok=$(dis::getAccessToken)
	fi	
	local result=`curl -s -X POST \
	"$DISTokenURL/onboarding/v2/partner/$PartnerclientID" \
	-H "Content-Type: application/json" \
	-H "Authorization: Bearer $oas_access_tok" \
	-d '{
		"idcsUrl": "'$WTSS_IDCS_HOST'",
		"tenantName": "'$TENANT_ID'"
	}'`
	OASTenantID=$(jq -r '.tenantId' <<< "$result")	
	echo "$OASTenantID"
}

dis::addDisCustomRole(){
	local access_token="$1"
	local actionMethod="$2"
	local disTenantID="$3"
	local custom_Role_Json="$4"
	local result=`curl -s -X $actionMethod \
	"$DISTokenURL/platform/visualization/v1/tenant/$disTenantID/oas/customrole" \
	-H "Authorization: Bearer $access_token" \
	-H "Content-Type: application/json" \
	-d "$custom_Role_Json"`
  echo "$result"
}

getSTSAdminToken(){
SAT=$(kubectl -n ${SUB_NAMESPACE} get secrets reserved-service-access-token -o jsonpath='{.data.sat}' | base64 -d)
WTSS_IDCS_HOST=$(kubectl -n ${SUB_NAMESPACE} get secret tenant-order -o jsonpath=\{.data."WTSS_IDCS_HOST"\} | base64 -d)
STS_S3_PATH="${BASE_SECRET_URLS}sts"
STATUS_RECEIVED=$(curl -4 --insecure -o sts_idcs_admin.json -w "%{http_code}" -X "GET" -H "Authorization: Bearer ${SAT}"  -H "Content-Type:application/json" ${STS_S3_PATH}-stripes)
echo "current status: $STATUS_RECEIVED" 
if [[ ${STATUS_RECEIVED} == "20"* ]]; then
	STS_S3_PATH="${STS_S3_PATH}-stripes"
	echo "INFO" "getSTSAdminToken.STS_S3_PATH - $STS_S3_PATH"
else
	curl -o sts.json --silent --fail -k -X "GET" -H "Authorization:Bearer $SAT" -H "Content-Type:application/json" "${STS_S3_PATH}"
	# sts_c_id=$(cat sts.json | python -c 'import sys, json; print json.load(sys.stdin)["client_id"]')
	# sts_c_secret=$(cat sts.json | python -c 'import sys, json; print json.load(sys.stdin)["client_secret"]')
	# allowedServiceTypesforLCM=$(cat sts.json | python -c 'import sys, json; print json.load(sys.stdin)["allowedServiceTypesforLCM"]')	
	
	sts_c_id=$(python3 -c 'import sys, json; print(json.load(sys.stdin)["client_id"])' < sts.json)
	sts_c_secret=$(python3 -c 'import sys, json; print(json.load(sys.stdin)["client_secret"])' < sts.json)
	allowedServiceTypesforLCM=$(python3 -c 'import sys, json; print(json.load(sys.stdin)["allowedServiceTypesforLCM"])' < sts.json)
cat <<EOF >sts_idcs_admin.json
[
      {
        "ci" : "${sts_c_id}",
        "cs" : "${sts_c_secret}",
        "self_update_url" : "${self_update_url}",
        "allowedServiceTypesforLCM":"${allowedServiceTypesforLCM}"
      }
]
EOF
echo "sts_idcs_admin.json file is:"
cat sts_idcs_admin.json
fi
echo "INFO" "getAdminToken.STS_S3_PATH - $STS_S3_PATH"
echo "INFO" "WTSS_IDCS_HOST - $WTSS_IDCS_HOST"
cat sts_idcs_admin.json
set +e
echo -n "false" > TOKEN_FOUND
jq -c '.[]' sts_idcs_admin.json | while read i; do
	sts_c_id=$(jq -r '.ci' <<< "$i")
	sts_c_secret=$(jq -r '.cs' <<< "$i")
	allowedServiceTypesforLCM=$(jq -r '.allowedServiceTypesforLCM' <<< "$i")
	self_update_url=$(jq -r '.self_update_url' <<< "$i")
	echo "INFO" "Trying with below values : "
	echo "INFO" "self_update_url      : $self_update_url"
	echo "INFO" "sts_c_id             : $sts_c_id"
	echo "INFO" "sts_c_secret         : $sts_c_secret"
	echo "INFO" "allowedServiceTypes  : $allowedServiceTypesforLCM"	

	admin_encoded=`echo -n "${sts_c_id}:${sts_c_secret}" | base64 -w 0`
	TIMEOUT=0
	COUNTDOWN=3
	while [ ${COUNTDOWN} -gt 0 ]; do
		echo "INFO" "Token request try: ${COUNTDOWN}"
		STATUS_RECEIVED=$(curl -k -s -o result.json -w "%{http_code}" -X POST -H "Authorization: Basic $admin_encoded" -d 'grant_type=client_credentials&scope=urn:opc:idm:__myscopes__' "${WTSS_IDCS_HOST}/oauth2/v1/token?IDCS_CLIENT_TENANT=${self_update_url}")
		if [[ ${STATUS_RECEIVED} == 200 ]]; then
			echo -n "true" > TOKEN_FOUND
			break;
		else
			sleep 5s;
		fi
		(( COUNTDOWN=COUNTDOWN-1 ))
	done
	   
		if [[ "$(cat TOKEN_FOUND)" != "true"* ]]; then
			continue;
		fi
                
        cat result.json        
		if [[ "$(cat TOKEN_FOUND)" == "true"* ]]; then
			break;
		fi
done
set -e
if [[ "$(cat TOKEN_FOUND)" != "true"* ]]; then
	echo "FATAL" "Failed to fetch access token [sts] after 3 retries"
	cat result.json
	exit 1
else
	#export admin_token=$(cat result.json | python -c 'import sys, json; print json.load(sys.stdin)[sys.argv[1]]' access_token)
	export admin_token=$(python3 -c 'import sys, json; print(json.load(sys.stdin)["access_token"])' < result.json)
fi
}

dis::enableOAC(){
	# To enable OAC access to OAS
	local DR="$1"
	local APP_NAME_UPPER="$2"
	local DIS_TENANT_ID="$3"
	local SUB_NAMESPACE="$4"

	getSTSAdminToken

	if [[ "${DR^^}" == "TRUE"* ]];
	then
		GBUA_APP_IDENTIFIER="$APP_NAME_UPPER"_OAS_"$DIS_TENANT_ID"_APPID_DR_APP_ID
	else
		GBUA_APP_IDENTIFIER="$APP_NAME_UPPER"_OAS_"$DIS_TENANT_ID"_APPID
	fi
	echo "INFO" "GBUA_APP_IDENTIFIER : ${GBUA_APP_IDENTIFIER}"

	identityServiceInstanceGuid=$(kubectl -n ${SUB_NAMESPACE} get cm wtss-config -o json |jq -r '.data."routes.json"' |jq -r .[].listeners.items[1].identityServiceInstanceGuid)
	echo "INFO" "identityServiceInstanceGuid : ${identityServiceInstanceGuid}"

	webtier_policy_update_status=$(curl --noproxy '*' --insecure --silent --location -o webtier_result.json -w "%{http_code}" -X "PATCH" \
	"$WTSS_IDCS_HOST/sm/v1/AppServices/$identityServiceInstanceGuid/$GBUA_APP_IDENTIFIER" \
	 -H "Authorization: Bearer $admin_token"\
	 -H "Content-Type: application/json"\
	 -d '{
		"command": "updateWebTierPolicyJson",
		"webTierPolicyJson": "{\"cloudgatePolicy\": {\"version\": \"2.6\",\"requireSecureCookies\": false,\"allowCors\": true,\"disableAuthorize\": false,\"webtierPolicy\": [{\"policyName\": \"default\",\"resourceFilters\": [{\"filter\": \"/onboarding/.*\",\"comment\": \"\",\"type\": \"regex\",\"method\": \"oauth\",\"authorize\": false},{\"filter\": \"/ingestion/.*\",\"comment\": \"\",\"type\": \"regex\",\"method\": \"oauth\",\"authorize\": false},{\"filter\": \"/'$DIS_TENANT_ID'/xmlpserver/services/?.*\",\"comment\": \"\",\"type\": \"regex\",\"method\": \"public\",\"authorize\": false},{\"filter\": \"/'$DIS_TENANT_ID'/analytics-ws/?.*\",\"comment\": \"\",\"type\": \"regex\",\"method\": \"public\",\"authorize\": false},{\"filter\": \"/'$DIS_TENANT_ID'/.*\",\"comment\": \"\",\"type\": \"regex\",\"method\": \"oauth\",\"authorize\": false},{\"filter\": \"/config/odi/.*\",\"comment\": \"\",\"type\": \"regex\",\"method\": \"oauth\",\"authorize\": false},{\"filter\": \"/platform/.*\",\"comment\": \"\",\"type\": \"regex\",\"method\": \"oauth\",\"authorize\": false}]}]}}"
	 }'
	 )

	APP_Update_Result=$(cat webtier_result.json)

	if [[  $webtier_policy_update_status == 20* ]];
	then
		echo "STATUS_RECEIVED========$webtier_policy_update_status"
		echo "INFO" "webtier policy update result : $APP_Update_Result"
		echo "webtier policy updated successfully to enable OAC access"
	else
		echo "STATUS_RECEIVED========$webtier_policy_update_status"
		echo "INFO" "webtier policy update result : $APP_Update_Result"
		echo "webtier policy update failed; unable to enable OAC access"
	fi

}

dis::enableBIP(){
	local actionMethod="$1"
	local disTenantID="$2"
	#local BIP_enable_JOSN="{"deploymentTemplate":["visualization"],"visualization":{"oas":[{"isBIPEnabled":true}]}}"

	oas_access_tok=$(dis::getAccessToken)
	BIP_Enable_result=$(curl -s -X $actionMethod \
	"$DISTokenURL/onboarding/v2/partner/$PartnerName/tenant/$disTenantID?fullPayload=false" \
	-H "Content-Type: application/json" \
	-H "Authorization: Bearer $oas_access_tok" \
	-d '{"deploymentTemplate":["visualization"],"visualization":{"oas":[{"isBIPEnabled":true}]}}'
	)
  	echo "$BIP_Enable_result"
}

dis::payloadUpdateStatus(){
	local actionMethod="$1"
	local oas_access_tok=$(dis::getAccessToken)
	local result=$(curl -s --location --request $actionMethod "$DISTokenURL/onboarding/v2/partner/$PartnerName/tenant/$DIS_TENANT_ID" \
	--header "accept: application/json" \
	--header "Content-Type: application/json" \
	--header "Authorization: Bearer $oas_access_tok"
	)
  echo "$result"
}

dis::updateDISTenantPartialPayload(){

	local actionMethod="$1"
	local disTenantID="$2"
	local payload="$3"
	oas_access_tok=$(dis::getAccessToken)
	update_payload_result=$(curl -s -X $actionMethod \
	"$DISTokenURL/onboarding/v2/partner/$PartnerName/tenant/$disTenantID?fullPayload=false" \
	--header 'accept: application/json' \
	--header 'Content-Type: application/json' \
	--header 'Authorization: Bearer '$oas_access_tok'' \
	--data @${payload})

  	echo "update_payload_result:- $update_payload_result"
}

