#!/bin/bash
set -e

wtss_restart(){
flow="$1"
dirctry="$2"
wtss_config_name="$3"
deploy_wtss_svc="$4"
routesjson="$dirctry/routes.json"

cat $routesjson  | jq '.' > $dirctry/config-cm/routes.json
kubectl -n ${SUB_NAMESPACE} create configmap $wtss_config_name --from-file=$dirctry/config-cm/ -o yaml --dry-run=client | kubectl  apply -f -
kubectl -n ${SUB_NAMESPACE} rollout restart deploy ${deploy_wtss_svc}

}

prepareWtssForExistingSvc(){
flow="$1"
dirctry="$2"
routesjson=$dirctry/routes.json

exists=$(cat $routesjson | jq '.[].listeners.items[] | select(.id=="'$serviceid'").pathPrefixes | index("/'$i'")')  
if [[ -z "$exists" || "$exists" == null ]];then
	 echo "endpoint for serviceid=$serviceid does not exits : $i"
	 echo $(cat $routesjson | jq -r '( .[].listeners.items[] | select(.id=="'$serviceid'") | .pathPrefixes ) += ["/'$i'"]') > "$routesjson"
	 if [[ "$service_host_type" == "dfcs" ]];then
		 echo $(cat $routesjson | jq -r '( .[].listeners.items[] | select(.id=="'$serviceid'") | .pathPrefixes ) += ["/'$TENANT_ID/$i'"]') > "$routesjson"
	 fi	 
	 logMessagePatch "INFO" "Service endpoint/s added successfully."
fi
}

prepareWtssToUpdate(){
flow="$1"
dirctry="$2"
wtssjson="$dirctry/config-cm/wtss-config.json"
routesjson="$dirctry/routes.json"

identty_instance_guid=$(jq -r '.idcs.tenant' $dirctry/config-cm/wtss-config.json)
files=("dfcs-services.txt" "common-service.txt")
for file in "${files[@]}"; do

cat "$file" | while IFS= read -r line || [ -n "$line" ]; do
line=$(echo "$line" | xargs)
if [[ -z "$line" ]]; then
continue
fi
serviceid=$(echo $line | cut -d ':' -f1 | sed "s/\"//g")
endpoints=$(echo $line | cut -d ':' -f2 | sed "s/\"//g")
service_host_type=$( [[ $file == *"dfcs"* ]] && echo "dfcs" || echo "fsafnd" )
target_port=$( [[ $file == *"dfcs"* ]] && echo "443" || echo "8080" )
serviceEntryExists=$(jq --arg id "$serviceid" '.[].listeners.items | map(select(.id == $id)) | length > 0' $routesjson)
service_host=$( [[ $file == *"dfcs"* ]] && echo "$FSGBU_DFCS_ELB" || echo "$serviceid" )
service_host=$(echo "$service_host" | sed 's/\/$//')

if [[ "$serviceEntryExists" == "false" ]];then
        echo $(echo "$serviceEntry" | sed \
                -e "s|__TARGET_PORT__|\"$target_port\"|" \
                -e "s|__IDENTTY_INSTANCE_GUID__|$identty_instance_guid|" \
                -e "s|__POLCY_IDENTTY_INSTANCE_GUID__|$identty_instance_guid|" \
                -e "s|__SERVICE_ID__|$serviceid|" \
                -e "s|__SERVICE_HOST__|$service_host|") > service_$serviceid.json

python <<END
import json
import os

		
def is_service_entry_present(filename, service_entry):
    with open(filename, 'r') as f:
        for line in f:
            if line.strip().startswith(service_entry):
                return True
    return False

def insert_entry_if_not_present(filename, service_entry, srvce_entry_line):
    lines = []
    with open(filename, 'r') as f:
        lines = f.readlines()
        
    while lines and not lines[-1].strip():
        lines.pop()
        
    if not is_service_entry_present(filename, service_entry):
        lines.append(srvce_entry_line + '\n')

    with open(filename, 'w') as f:
        f.writelines(lines)

serviceid = "$serviceid"
service_file_name = "service_$serviceid.json"
routes_json_file = '$routesjson'
print('routes_json_file=----'+routes_json_file)
new_path_prefixes = []
endpoints='$endpoints'
endpoints=endpoints.split("~")
for endpoint in endpoints:
        new_path_prefixes.append('/'+endpoint)
        if "dfcs" in '$file':
                new_path_prefixes.append("/${TENANT_ID}/"+endpoint)

with open(service_file_name, 'r') as f:
        service_data=json.load(f)
service_data["pathPrefixes"].extend(new_path_prefixes)
with open(service_file_name, 'w') as f:
    json.dump(service_data, f, indent=2)

with open(routes_json_file, 'r') as routes_file:
    routes_data = json.load(routes_file)

routes_data[0]['listeners']['items'].append(service_data)
with open(routes_json_file, 'w') as routes_file:
    json.dump(routes_data, routes_file, indent=2)

if '$flow' != "wtss-int":
    with open("service-properties.props", "r") as f:
            sp_lines = f.readlines()
            f.close()

    with open("service-properties-secondary.props", "r") as f:
            sps_lines = f.readlines()
            f.close()

    if "dfcs" in '$file':
            sp_entry='$serviceid'+'='+'$FSGBU_DFCS_ELB'+':$target_port'+'/$TENANT_ID'
            insert_entry_if_not_present('service-properties.props', serviceid + "=", sp_entry)
    else:
            sp_entry='$serviceid'+'='+'http://'+'$serviceid'+':$target_port'
            sps_entry='$serviceid'+'='+'$external_load_balancer'+'/$TENANT_ID'+'-int'
            insert_entry_if_not_present('service-properties.props', serviceid + "=", sp_entry)
            insert_entry_if_not_present('service-properties-secondary.props', serviceid + "=", sps_entry)
END
if [[ $flow == "wtss" ]];then
if [ -f service-properties.props ];then
	kubectl -n ${SUB_NAMESPACE} create secret generic service-properties --from-file=./service-properties.props -o yaml --dry-run=client | kubectl  apply -f - &>/dev/null
fi
if [ -f service-properties-secondary.props ];then
	kubectl -n ${SUB_NAMESPACE} create secret generic service-properties-secondary --from-file=./service-properties-secondary.props -o yaml --dry-run=client | kubectl  apply -f - &>/dev/null
fi
fi
logMessagePatch "INFO" "New service( $serviceid ) entry added successfully for ${flow}."
else
for i in $(echo $endpoints | sed "s/~/ /g")
do
    prepareWtssForExistingSvc "$flow" "$dirctry"
done
fi
done
done
}

refreshDFCSServiceProps(){
	logMessagePatch "INFO" "SUB_NAMESPACE : $SUB_NAMESPACE"
	logMessagePatch "INFO" "Entered refreshDFCSServiceProps method"
	patch::initialize
	
	if [[ $UPDATE_WTSS != "" && $UPDATE_WTSS == "false" ]];then
		logMessagePatch "INFO" "WTSS deployment is not enabled for this patch. Skipping the stage"
		exit 0
	fi
	./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -f $deployments_artifacts_path ./ -e k8s
	
	if [[ -f "common-service.txt" && -f "dfcs-services.txt" ]];then
	patch::replace_placeholders "common-service.txt"
	kubectl  -n ${SUB_NAMESPACE} get secrets service-properties  -o jsonpath='{.data.service-properties-secondary\.props}' | base64 -d > service-properties-secondary.props
    echo "-------------service-properties-secondary.props--------------"
    cat service-properties-secondary.props
    echo "---------------------------------------------------"
    files=("dfcs-services.txt" "common-service.txt")
    for file in "${files[@]}"; do
        while IFS= read -r line || [ -n "$line" ]; do
            line=$(echo "$line" | xargs)
            if [[ -z "$line" ]]; then
                continue
            fi
            serviceid=$(echo $line | cut -d ':' -f1 | sed "s/\"//g")
            target_port=$(echo $line | cut -d ':' -f3 | sed "s/\"//g")
            # target_port=$(echo "$line" | cut -d ':' -f3)
            # target_port="8080"
            if [[ -z "$target_port" ]]; then
                target_port="8080"
            fi

python <<END
import json
import os

changed = False
serviceid = "$serviceid"
target_port = """$target_port"""

def is_service_entry_present(filename, service_entry):
    with open(filename, 'r') as f:
        for line in f:
            if line.strip().startswith(service_entry):
                return True
    return False

def insert_entry_if_not_present(filename, service_entry, srvce_entry_line):
    global changed
    count = 0
    lines = []
    with open(filename, 'r') as f:
        lines = f.readlines()
        
    while lines and not lines[-1].strip():
        lines.pop()
        
    if not is_service_entry_present(filename, service_entry):
        lines.append(srvce_entry_line + '\n')
        changed = True

    with open(filename, 'w') as f:
        f.writelines(lines)

serviceid = "$serviceid"

with open("service-properties-secondary.props", "r") as f:
    sp_lines = f.readlines()
    f.close()

if "dfcs" in '$file':
    sp_entry = f'{serviceid}=http://{serviceid}:{target_port}'
    insert_entry_if_not_present('service-properties-secondary.props', serviceid + "=", sp_entry)
else:
    sps_entry='$serviceid'+'='+'$primary_lb_dns'+'/$TENANT_ID'+'-int'
    insert_entry_if_not_present('service-properties-secondary.props', serviceid + "=", sps_entry)

if changed:
    with open('props_changed.flag', 'w') as f:
        f.write('yes')
END
        done < "$file"
      # Only proceed if the file was actually changed
            if [[ -f props_changed.flag ]]; then
                    if [ -f service-properties-secondary.props ]; then
                        logMessagePatch "INFO" "service-properties-secondary.props after modification:"
                        echo "-------service-properties-secondary.props---------------"
					    cat service-properties-secondary.props
                        echo "----------------------------------------------"
                        # kubectl -n ${SUB_NAMESPACE} create secret generic service-properties --from-file=./service-properties.props -o yaml --dry-run=client | kubectl apply -f - &>/dev/null
                        kubectl -n ${SUB_NAMESPACE} create secret generic service-properties --from-file=./service-properties-secondary.props -o yaml --dry-run=client | kubectl  apply -f - &>/dev/null

                    fi
                logMessagePatch "INFO" "Service Props file refreshed"
                rm -f props_changed.flag
            fi
    done
	else
	logMessagePatch "WARN"  "Skipping execution, Configuration artifacts not present for Stage - $PATCH_STAGE"
	fi

}

wtss_registry() {
	
	logMessagePatch "INFO" "VARIABLES: $DEPLOY_ENV"
	logMessagePatch "INFO" "Entered wtss registry method"
	patch::initialize
	
	if [[ $UPDATE_WTSS != "" && $UPDATE_WTSS == "false" ]];then
		logMessagePatch "INFO" "WTSS deployment is not enabled for this patch. Skipping the stage"
		exit 0
	fi
	./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -f $deployments_artifacts_path ./ -e k8s
	
	if [[ -f "common-service.txt" && -f "dfcs-services.txt" ]];then
	patch::replace_placeholders "common-service.txt"
	
	mkdir -p config-cm
	mkdir -p config-cm-int/config-cm
	workdir="$(pwd)"
	intdir="$workdir/config-cm-int"
	
	kubectl  -n ${SUB_NAMESPACE} get configmap wtss-config -o jsonpath='{.data.routes\.json}' > routes.json
	kubectl  -n ${SUB_NAMESPACE} get configmap wtss-config -o jsonpath='{.data.wtss-config\.json}' > ./config-cm/wtss-config.json
	
	kubectl  -n ${SUB_NAMESPACE} get configmap wtss-config-int -o jsonpath='{.data.routes\.json}' > $intdir/routes.json
	kubectl  -n ${SUB_NAMESPACE} get configmap wtss-config-int -o jsonpath='{.data.wtss-config\.json}' > $intdir/config-cm/wtss-config.json
	
	kubectl  -n ${SUB_NAMESPACE} get secrets service-properties  -o jsonpath='{.data.service-properties\.props}' | base64 -d > service-properties.props
	kubectl  -n ${SUB_NAMESPACE} get secrets service-properties-secondary  -o jsonpath='{.data.service-properties-secondary\.props}' | base64 -d > service-properties-secondary.props
	#Remove
	#jq '.[0].listeners.items |= map(select(.id != "rse"))' routes.json > routes_modified.json && mv routes_modified.json routes.json
	serviceEntry='{
	"targetServicePort": __TARGET_PORT__,
	"cloudgatePolicyName": "default",
	"identityServiceInstanceGuid": "__IDENTTY_INSTANCE_GUID__",
	"policyIdentityServiceInstanceGuid": "__POLCY_IDENTTY_INSTANCE_GUID__",
	"pathPrefixes": [],
	"id": "__SERVICE_ID__",
	"targetServiceHostname": "__SERVICE_HOST__"
	}'
	
	prepareWtssToUpdate "wtss" "$workdir"
	prepareWtssToUpdate "wtss-int" "$intdir"
	wtss_restart "wtss" "$workdir" "wtss-config" "$TENANT_ID"
	wtss_restart "wtss-int" "$intdir" "wtss-config-int" "${TENANT_ID}-int"
	set +e
	echo "wtss updated..."
	else
	logMessagePatch "WARN"  "Skipping execution, Configuration artifacts not present for Stage - $PATCH_STAGE"
	fi
}

#start
logMessagePatch "INFO" "Current Cluster : $MSP_PROJ_NAME"
if [[ "$MSP_PROJ_NAME" == "fsgbu-dfcs" ]]; then
refreshDFCSServiceProps
else
wtss_registry
fi