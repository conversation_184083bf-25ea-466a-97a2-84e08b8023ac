export CORP_REGISTRY="phx.ocir.io/oraclegbudevcorp"
export CORP_REPO="https://${CORP_REGISTRY}"
export CI_DEPLOYER_IMAGE_NAME="deployer:latest"
export CI_DEPLOYER_IMAGE_PATH="sdaas/${CI_DEPLOYER_IMAGE_NAME}"
export CI_DEPLOYER_IMAGE_ABS_PATH="${CORP_REGISTRY}/cn-shared/${CI_DEPLOYER_IMAGE_PATH}"

export BASE_OL7_IMAGE_PATH="baseimages/baseimages-flask3:1.7.0"
export BASE_OL7_IMAGE_ABS_PATH="${CORP_REGISTRY}/cn-shared/${BASE_OL7_IMAGE_PATH}"

unset http_proxy
unset https_proxy
unset no_proxy

corp_http_proxy=http://www-proxy-adcq7.us.oracle.com:80
export http_proxy="${corp_http_proxy}"
export https_proxy="${corp_http_proxy}"
export no_proxy="localhost,127.0.0.1,.us.oracle.com,.ocir.io"

export CI_PROJECT_NAME="${CI_PROJECT_NAME}"
 
[[ -v CI_PROJECT_NAMESPACE && -n ${CI_PROJECT_NAMESPACE// } ]] && \
  export oracle_guid=${CI_PROJECT_NAMESPACE,,} || \
  export oracle_guid=$(whoami | tr [:upper:] [:lower:])
 
export SHARED_BUCKET="fsgbu-fsafnd-shared"
export MSP_COST_CENTER="fsgbu"
export MSP_SERVICE="__MSP_SERVICE__"

export OST_CLASS=__OST_CLASS__
export TENANT_ID="__TENANT_ID__"
export PATCH_VERSION="__PATCH_VERSION__"
export PATCH_ID=__PATCH_ID__
export PATCH_ARTSTORE_ARTIFACT_PATH="dfcs-upgrade-pipeline-artifacts"
export DIS_ARTIFACT_DEFAULT_VERSION=21.12.6

export IS_ADW=__IS_ADW__
export IMAGE_VERSION="__IMAGE_VERSION__"
export CS_IMAGE_NAME="dfcs-upgrade-pipeline"
export CS_IMAGE_PATH="${CS_IMAGE_NAME}:${IMAGE_VERSION}"
export CS_IMAGE_FSAFND_ABS_PATH="${CORP_REGISTRY}/cn-fsgbu-fsafnd/${CS_IMAGE_PATH}" 
export CS_IMAGE_DFCS_ABS_PATH="${CORP_REGISTRY}/cn-fsgbu-dfcs/${CS_IMAGE_PATH}" 


# Create artstore context for subsequent commands
artstore --version
artstore config use default > /dev/null
artstore -p="${MSP_PROJ_NAME}" -k=${TOKEN} config set ${MSP_PROJ_NAME}
