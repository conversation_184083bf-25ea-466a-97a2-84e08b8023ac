FROM container-registry.oracle.com/os/oraclelinux:8-slim as dfcs-upgrade-pipeline
ARG corp_http_proxy=http://www-proxy.idc.oracle.com:80
#ARG corp_http_proxy=http://www-proxy.us.oracle.com:80
ARG no_proxy="localhost,127.0.0.1,.us.oracle.com,.ocir.io"
ARG user_name=cnedfcsdev
ARG user_home=/home/<USER>
COPY artifacts/ ${user_home}/


COPY instantclient-basic-linux.x64-********.0dbru.zip instantclient-sqlplus-linux.x64-********.0dbru.zip ./

RUN set -eux ; \
    export http_proxy=${corp_http_proxy}; \
    export https_proxy=${corp_http_proxy}; \
    microdnf install -y libnsl  libaio zip tar gzip unzip openssl findutils glibc libgcc python3.9 expect vim shadow-utils python2;\
    unzip instantclient-basic-linux.x64-********.0dbru.zip && \
    unzip -o instantclient-sqlplus-linux.x64-********.0dbru.zip && \
    mv instantclient_18_5/* /usr/lib/ && \
    rm instantclient-basic-linux.x64-********.0dbru.zip && \
    rm instantclient-sqlplus-linux.x64-********.0dbru.zip && \
    ln -sf /usr/lib/libclntsh.so.18.1 /usr/lib/libclntsh.so && \
    ln -sf /usr/lib/libocci.so.18.1 /usr/lib/libocci.so && \
    ln -sf /usr/lib//libociei.so /usr/lib/libociei.so && \
    ln -sf /usr/lib//libnnz18.so /usr/lib/libnnz18.so ; \
    ln -sf /usr/lib//libsqlplus.so /usr/lib/libsqlplus.so && \
    ln -sf /usr/lib//libsqlplusic.so /usr/lib/libsqlplusic.so ; \
    cd /usr/lib/ ; \
    chmod u-s sqlplus ; \
    chmod g-s sqlplus ; \
    rm -rf /usr/share/doc/vim-common/Changelog.rpm; \
    microdnf clean all ; \
    echo "Instant utis & client 18_5 installation done ";    

RUN chmod +x /usr/lib/sqlplus /usr/lib/libsqlplus.so \
    && chmod -R 755 /usr/lib/ ;

user root
RUN set -eux ;\
    export http_proxy=${corp_http_proxy} \
    && export https_proxy=${corp_http_proxy} \
    && export no_proxy=${no_proxy} \
    && mkdir -p ${user_home}/bin/ \
    && cd ${user_home}/bin/ \
    && curl -LO "https://storage.googleapis.com/kubernetes-release/release/$(curl -s https://storage.googleapis.com/kubernetes-release/release/stable.txt)/bin/linux/amd64/kubectl" \
    && ls -la ./ \
    && chmod +x ./kubectl \
    && chmod +x kubectl \
    && useradd -m -u 1000 ${user_name} \
    && chown -R ${user_name}:${user_name} ${user_home} \
    && chown -R ${user_name}:${user_name} ${user_home}/bin \
    && export PATH=${user_home}/bin:${PATH} \    
    && kubectl version --client \
    && cd ${user_home};

user root
WORKDIR ${user_home}
RUN set -eux ;\
    export http_proxy=${corp_http_proxy}; \
    export https_proxy=${corp_http_proxy}; \
    curl -LO https://github.com/jqlang/jq/releases/download/jq-1.5/jq-linux64 && mv jq-linux64 jq && chmod +x jq; \
    curl --noproxy '*' -L http://gbu-files.us.oracle.com/software/artstore/artstore_linux_amd64_ol7 -o /usr/local/bin/artstore; \
    chmod 755 /usr/local/bin/artstore;

user ${user_name}
WORKDIR ${user_home}
    
user ${user_name}
RUN set -eux ; \
    export http_proxy=${corp_http_proxy} \
    && export https_proxy=${corp_http_proxy} \    
    && python3 -m pip install --upgrade pip \ 
    && python3 -m pip install requests \
    && python3 -m pip install pyyaml \
    && python3 -m pip install oci-cli \
    && python3 -m pip install oracledb --upgrade \  
    && python3 -m pip install --upgrade oci \
    && export PATH=${user_home}/.local/bin:${PATH}; \
    cd ${user_home};\
    rm -rf ${user_home}/.local/lib/python3.9/site-packages/pip/_vendor/distlib/t32.exe; \
    rm -rf ${user_home}/.local/lib/python3.9/site-packages/pip/_vendor/distlib/t64-arm.exe; \
    rm -rf ${user_home}/.local/lib/python3.9/site-packages/pip/_vendor/distlib/t64.exe; \
    rm -rf ${user_home}/.local/lib/python3.9/site-packages/pip/_vendor/distlib/w32.exe; \
    rm -rf ${user_home}/.local/lib/python3.9/site-packages/pip/_vendor/distlib/w64-arm.exe; \
    rm -rf ${user_home}/.local/lib/python3.9/site-packages/pip/_vendor/distlib/w64.exe; \
    echo "Installation complete";

user ${user_name}
WORKDIR ${user_home}
ENV PATH=${user_home}:${user_home}/bin:${PATH}
ENV PATH="/usr/lib:${PATH}" \
    LD_LIBRARY_PATH="/usr/lib:${LD_LIBRARY_PATH}"
