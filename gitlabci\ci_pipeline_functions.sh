#!/bin/bash
source ./artifacts/common_functions.sh
mkdir -p artifacts/env/${MSP_PROJ_NAME}
PATCH_HOME="$(pwd)"
IMAGE_VERSION=${IMAGE_VERSION:-$PATCH_VERSION}

logMessage(){
	Datelog=$(date +%Y-%m-%d)
    timestamplog=$(date +%H:%M:%S)
    Targethost=$HOSTNAME
    severity="$1"
    LoggerName="DFCS_PATCH_UPGRADE"
    messageId="$3"
	messagebody="$2"
	if [[ ${severity^^} == "INFO" ]];then
		echo -e "\033[1;32m$Datelog $timestamplog  $LoggerName -- $severity -- $messageId -- $messagebody\033[0m"
	elif [[ ${severity^^} == "WARN" || ${severity^^} == "WARNING" ]];then
		echo -e "\033[1;38;5;214m$Datelog $timestamplog  $LoggerName -- $severity -- $messageId -- $messagebody\033[0m"
	elif [[ ${severity^^} == "ERROR" || ${severity^^} == "FATAL"  ]];then
		echo -e "\033[1;31m$Datelog $timestamplog -- $severity -- $messageId -- $messagebody\033[0m"
	else
		echo -e "$Datelog $timestamplog  $LoggerName -- $severity -- $messageId -- $messagebody"
	fi
}

initialize(){
	deployments=dfcs-deployment-artifacts/$PATCH_VERSION
	logMessage "INFO" "Deployments Artifacts path: $deployments"
	
	envfile="$deployments/env/$MSP_PROJ_NAME/pipeline-${OST_ENVIRONMENT}.env"
	logMessage "INFO" "envfile path: $envfile"
	
	artstore -p ${MSP_PROJ_NAME} file pull -f $envfile ./artifacts/env/${MSP_PROJ_NAME}/
	fileName=`basename "$envfile"`
	REGION_CODE=$(cat ./artifacts/env/${MSP_PROJ_NAME}/$fileName | grep -w REGION_CODE)
	REGION_CODE=$(echo ${REGION_CODE} | cut -d '=' -f2 | sed "s/\"//g")
	
	if [[ (${PATCH_STAGE^^} != "PUBLISH" || ${PATCH_STAGE^^} != "SCAN" || ${PATCH_STAGE^^} != "SYNC") ]];then
		artstore -p ${MSP_PROJ_NAME} file pull -rf $deployments/payload.json ./	
		chmod 755 ./artifacts/parse_json_payload.py
		./artifacts/parse_json_payload.py "payload.json" "$PATCH_HOME" "$OST_ENVIRONMENT" "$OST_CLASS" "$TENANT_ID"
		sed -i \
			-e "s|__PATCH_ID__|${PATCH_ID}|g" \
		   	$PATCH_HOME/patch_pipeline_services.env				
		source $PATCH_HOME/patch_pipeline_services.env	
	fi
	logMessage "INFO" "All CI side Env Loaded..."
}

set_ci_params() {	
	MSP_PROJ_NAME="${1}"
	sed	\
		-e "s|__MSP_PROJ_NAME__|${1}|g" \
		-e "s|__ARTPROJECT__|${1}|g" \
		-e "s|__PATCH_VERSION__|${PATCH_VERSION}|g" \
		-e "s|__OST_ENVIRONMENT__|${OST_ENVIRONMENT}|g" \
		-e "s|__OST_CLASS__|${OST_CLASS}|g" \
		-e "s|__MSP_SERVICE__|${MSP_SERVICE}|g" \
		-e "s|__PATCH_STAGE__|${PATCH_STAGE}|g" \
		-e "s|__PATCH_ID__|${PATCH_ID}|g" \
		-e "s|__PATCH_ARTSTORE_ARTIFACT_PATH__|${PATCH_ARTSTORE_ARTIFACT_PATH}|g" \
		-e "s|__TENANT_ID__|${TENANT_ID}|g" \
		-e "s|__TENANT_STRING__|${TENANT_STRING}|g" \
		-e "s|__PATCH_BRANCH__|${PATCH_BRANCH}|g" \
		-e "s|__PATCH_SCRIPT__|${2}|g" \
		-e "s|__IS_ADW__|${IS_ADW}|g" \
		-e "s|__TENANCY__|${TENANCY}|g" \
		-e "s|__ONBOARD_DIS_TENANT__|${ONBOARD_DIS_TENANT}|g" \
		-e "s|__CI_DIS_ARTIFACT_VERSION__|${CI_DIS_ARTIFACT_VERSION}|g" \
		-e "s|__DR__|${DR}|g" \
		-e "s|__DIS_FORCE_DELETE__|${DIS_FORCE_DELETE}|g" \
		-e "s|__IS_MAINTENANCE_ENABLED__|${IS_MAINTENANCE_ENABLED}|g" \
		-e "s|__DV_ACTION__|${DV_ACTION}|g" \
		-e "s|__CI_PIPELINE_ID__|${CI_PIPELINE_ID}|g" \
		-e "s|__API_POLL_COUNT__|${API_POLL_COUNT}|g" \
		-e "s|__API_POLL_INTERVAL__|${API_POLL_INTERVAL}|g" \
		-e "s|__REGION_CODE__|${REGION_CODE}|g" \
		-e "s|__PATCH_ACTION__|${PATCH_ACTION}|g" \
		wos/deploy.yaml > ${deployer_config_yaml}	
	
	cat ${deployer_config_yaml}
}

tool::deploy() {
	if [ "${GITLAB_CI}" = "true" ]; then
	  /usr/OST/app/deployer changeJob \
		  --summary "${JIRA_SUMMARY_DESC_STRING}" \
		  --description "${JIRA_SUMMARY_DESC_STRING}" \
		  ${deployer_config_yaml}
	else
	  artstore -p shared image pull -f /${CI_DEPLOYER_IMAGE_PATH}
	  docker run -t --rm -v ${deployer_config_yaml}:/usr/OST/app/etc/config.yaml:ro \
		  -e WF_JWT \
		  ${CI_DEPLOYER_IMAGE_ABS_PATH} /usr/OST/app/deployer changeJob \
		  --summary "${JIRA_SUMMARY_DESC_STRING}" \
		  --description "${JIRA_SUMMARY_DESC_STRING}" \
		  /usr/OST/app/etc/config.yaml
	fi
}

patch::deploy_services(){
	export WF_JWT="$1"
	TMPDIR=$(mktemp -d) || exit 1
	trap "rm -rf $TMPDIR" EXIT
	export deployer_config_yaml=${TMPDIR}/deploy.yaml
	set_ci_params "$2" "$3"
	export JIRA_SUMMARY_DESC_STRING="$2 ${CI_PROJECT_NAME} WOS Deploy"
	tool::deploy
	if [ $? -ne 0 ]; then
		exit -1
	fi 	
}

patch::disable_dv(){
	DV_ACTION="start"
	initialize
	if [[ -z $ON_OFF_DV_CONFIG || $ON_OFF_DV_CONFIG == "false" ]];then
		logMessage "INFO" "DV Status update has not been enabled for this patch. Skipping the stage"
		exit 0
	fi
	patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "update_dv_status.sh" 
	logMessage "INFO" "Disable DV stage complete."
}

export WF_JWT=${FSAFND_DAT}
TENANT_ID=${TENANT_ID:-NA}
