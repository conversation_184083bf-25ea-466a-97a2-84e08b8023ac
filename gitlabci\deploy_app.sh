#!/bin/bash

chmod +x ./gitlabci/ci_pipeline_functions.sh
source ./gitlabci/ci_pipeline_functions.sh

if [[ -z "$TENANT_ID" ]];then
	logMessage "ERROR" "TENANT_ID is Mandatory."
	exit -1
fi

case "${PATCH_STAGE}" in
   "disableDataVault") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::disable_dv
   ;;
   *) logMessage "ERROR" "Unsupported PATCH_STAGE: $PATCH_STAGE. Only 'disableDataVault' is supported."
	exit -1
   ;;
esac

