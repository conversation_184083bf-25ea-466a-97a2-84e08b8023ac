#!/bin/bash
set +x
PATCH_HOME=$(mktemp -d) || exit 1
trap "rm -rf $PATCH_HOME" EXIT

chmod +x ./gitlabci/ci_pipeline_functions.sh
source ./gitlabci/ci_pipeline_functions.sh

export WF_JWT=${FSAFND_DAT}
if [ -d artifacts ];then
	curl --noproxy "*" -o ./artifacts/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
fi

artstore -p ${SHARED_BUCKET} file pull cloudlab-dumps/instantclient-sqlplus-linux.x64-********.0dbru.zip ./ -f
artstore -p ${SHARED_BUCKET} file pull cloudlab-dumps/instantclient-basic-linux.x64-********.0dbru.zip ./ -f	

VERSION=$PATCH_VERSION
logMessage "INFO" "OST_CLASS: $OST_CLASS"

push_artifacts(){
	
	export WF_JWT="$2"	
	export MSP_PROJ_NAME=${1}
	deployer_config_yaml=${PATCH_HOME}/sync-definition.yaml
	sed \
		-e "s|__MSP_PROJ_NAME__|${MSP_PROJ_NAME}|g" \
		-e "s|__PATCH_VERSION__|${PATCH_VERSION}|g" \
		-e "s|__PATCH_ARTSTORE_ARTIFACT_PATH__|${PATCH_ARTSTORE_ARTIFACT_PATH}|g" \
		-e "s|__REGION__|${REGION}|g" \
		-e "s|__TENANCY__|${TENANCY}|g" \
		wos/sync-definition-${OST_CLASS}.yaml > ${deployer_config_yaml}

	mkdir -p artifacts/env/${MSP_PROJ_NAME}
	artstore -p ${MSP_PROJ_NAME} file push -rf artifacts ${PATCH_ARTSTORE_ARTIFACT_PATH}/${VERSION}
	artstore -p ${MSP_PROJ_NAME} -b sync file push -f ${deployer_config_yaml} ${PATCH_ARTSTORE_ARTIFACT_PATH}/${VERSION}
	cat $TOKEN > ${CI_PROJECT_DIR}/Token
	if [[ ${MSP_PROJ_NAME} == "fsgbu-fsafnd" ]];then
		CS_IMAGE_ABS_PATH="$CS_IMAGE_FSAFND_ABS_PATH"
	else
		CS_IMAGE_ABS_PATH="$CS_IMAGE_DFCS_ABS_PATH"
	fi
	docker build --pull \
		-t ${CS_IMAGE_ABS_PATH} \
		-f dockerfile.dfs 

	artstore -p ${MSP_PROJ_NAME} image push -f ${CS_IMAGE_ABS_PATH}
	artstore -p ${MSP_PROJ_NAME} image list "${CS_IMAGE_ABS_PATH}"
	
}

push_artifacts "fsgbu-fsafnd" "${FSAFND_DAT}"
push_artifacts "fsgbu-dfcs" "${DFCS_DAT}"
logMessage "INFO" "Published Patch utility Artifacts.."


