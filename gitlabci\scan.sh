#!/bin/bash
chmod +x ./gitlabci/ci_pipeline_functions.sh
source ./gitlabci/ci_pipeline_functions.sh

# Create a temporary directory and will be deleted on exit
TMPDIR=$(mktemp -d) || exit 1
trap "rm -rf $TMPDIR" EXIT
deployer_config_yaml=${TMPDIR}/config.yaml

tool::deploy() {
	if [ "${GITLAB_CI}" = "true" ]; then
  /usr/OST/app/deployer scan \
      --summary "${JIRA_SUMMARY_DESC_STRING}" \
      --description "${JIRA_SUMMARY_DESC_STRING}" \
      ${deployer_config_yaml}  
  else
    artstore -p shared image pull -f /${CI_DEPLOYER_IMAGE_PATH}
    docker run -t --rm -v ${deployer_config_yaml}:/usr/OST/app/etc/config.yaml:ro \
        -e WF_JWT \
        ${CI_DEPLOYER_IMAGE_ABS_PATH} /usr/OST/app/deployer scan \
        --summary "${JIRA_SUMMARY_DESC_STRING}" \
        --description "${JIRA_SUMMARY_DESC_STRING}" \
        /usr/OST/app/etc/config.yaml
  fi
  echo "artifacts SCAN completed."
}

patch:scan(){
  export WF_JWT="$1"
  export MSP_PROJ_NAME="$2"
  export JIRA_SUMMARY_DESC_STRING="${MSP_PROJ_NAME} ${CI_PROJECT_NAME} WOS San"

# initiate sync
cat > ${deployer_config_yaml} << EOF
apiVersion: 5
Project: ${MSP_PROJ_NAME}
scan:
  syncDefinitionPath: cn-${MSP_PROJ_NAME}-sync:${PATCH_ARTSTORE_ARTIFACT_PATH}/${VERSION}/sync-definition.yaml
  types: 
    - owasp vulnerability
    - third party approval
    - clamav
EOF

echo -e "==== Using scan Config ====\n"
echo "OST_CLASS: $OST_CLASS"
cat ${deployer_config_yaml}
tool::deploy
}
patch:scan "${FSAFND_DAT}" "fsgbu-fsafnd"
patch:scan "${DFCS_DAT}" "fsgbu-dfcs"
