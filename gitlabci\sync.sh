#!/bin/bash
chmod +x ./gitlabci/ci_pipeline_functions.sh
source ./gitlabci/ci_pipeline_functions.sh

export WF_JWT=${FSAFND_DAT}
export JIRA_SUMMARY_DESC_STRING="${MSP_PROJ_NAME} ${CI_PROJECT_NAME} WOS Sync"

# Create a temporary directory and will be deleted on exit
TMPDIR=$(mktemp -d) || exit 1
trap "rm -rf $TMPDIR" EXIT
deployer_config_yaml=${TMPDIR}/config.yaml

tool::deploy() {
  if [ "${GITLAB_CI}" = "true" ]; then
    /usr/OST/app/deployer sync \
        --summary "${JIRA_SUMMARY_DESC_STRING}" \
        --description "${JIRA_SUMMARY_DESC_STRING}" \
        ${deployer_config_yaml}
    
  else
    artstore -p shared image pull -f /${CI_DEPLOYER_IMAGE_PATH}
    docker run -t --rm -v ${deployer_config_yaml}:/usr/OST/app/etc/config.yaml:ro \
        -e WF_JWT \
        ${CI_DEPLOYER_IMAGE_ABS_PATH} /usr/OST/app/deployer sync \
        --summary "${JIRA_SUMMARY_DESC_STRING}" \
        --description "${JIRA_SUMMARY_DESC_STRING}" \
        /usr/OST/app/etc/config.yaml
  fi

echo "artifact sync completed."
} 

patch:sync(){
  export WF_JWT="$1"
  export MSP_PROJ_NAME="$2"
  export JIRA_SUMMARY_DESC_STRING="${MSP_PROJ_NAME} ${CI_PROJECT_NAME} WOS San"

cat > ${deployer_config_yaml} << EOF
apiVersion: 3
Project: ${MSP_PROJ_NAME}
streamOutput: True
sync:
  syncDefinitionPath: cn-${MSP_PROJ_NAME}-sync:${PATCH_ARTSTORE_ARTIFACT_PATH}/${VERSION}/sync-definition.yaml
EOF

echo -e "==== Using sync Config ====\n"
echo "OST_CLASS: $OST_CLASS"
cat ${deployer_config_yaml}
tool::deploy
}
patch:sync "${FSAFND_DAT}" "fsgbu-fsafnd"
patch:sync "${DFCS_DAT}" "fsgbu-dfcs"
