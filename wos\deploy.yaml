---
apiVersion: 1
Project: __MSP_PROJ_NAME__
streamOutput: True
changeJob:
  changeRequest:
    changeType: Planned Sprint - Story based
    services: __MSP_SERVICE__
  environment: __OST_ENVIRONMENT__
  class: __OST_CLASS__
  type: Standard
  jobs:
    - timeout: 10800
      parameters:
        deployParams:
          data:
            ARTPROJECT: __MSP_PROJ_NAME__
            PATH: __PATCH_ARTSTORE_ARTIFACT_PATH__/__PATCH_VERSION__
            ACTION: deploy
            DEPLOYSETS:
               - SETUP_SCRIPT: apply_upgrade_and_patch.sh
                 NAMESPACE: '__MSP_PROJ_NAME__'                
        templateParams:
          data:
            COMMON_NAMESPACE: '__MSP_PROJ_NAME__'
            PATCH_VERSION: '__PATCH_VERSION__'
            OST_ENVIRONMENT: '__OST_ENVIRONMENT__'
            ART_PROJECT: __MSP_PROJ_NAME__
            ARTSTORE_PATH: __PATCH_ARTSTORE_ARTIFACT_PATH__/__PATCH_VERSION__
            MSP_PROJ_NAME: __MSP_PROJ_NAME__
            PATCH_STAGE: __PATCH_STAGE__
            PATCH_ID: __PATCH_ID__
            TENANT_ID: __TENANT_ID__
            TENANT_STRING: __TENANT_STRING__
            PATCH_BRANCH: __PATCH_BRANCH__
            PATCH_SCRIPT: __PATCH_SCRIPT__
            OST_CLASS: __OST_CLASS__
            ARTPROJECT: __ARTPROJECT__
            IS_ADW: __IS_ADW__
            TENANCY: __TENANCY__
            REGION_CODE: __REGION_CODE__
            ONBOARD_DIS_TENANT: __ONBOARD_DIS_TENANT__
            CI_DIS_ARTIFACT_VERSION: __CI_DIS_ARTIFACT_VERSION__
            DIS_FORCE_DELETE: __DIS_FORCE_DELETE__
            DR: __DR__
            IS_MAINTENANCE_ENABLED: __IS_MAINTENANCE_ENABLED__
            DV_ACTION: __DV_ACTION__
            CI_PIPELINE_ID : __CI_PIPELINE_ID__
            API_POLL_INTERVAL : __API_POLL_INTERVAL__
            API_POLL_COUNT : __API_POLL_COUNT__
            PATCH_ACTION: __PATCH_ACTION__
